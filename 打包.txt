#单文件打包
1.pyinstaller  --icon=app.ico  --windowed --onefile --name DvrSocker MainApp.py
2.pyinstaller .\DvrSocker.spec 
note:单文件打包会有两个进程

#多文件
1.pyinstaller  --icon=app.ico  --windowed --name DvrSocker MainApp.py
2.pyinstaller .\DvrSocker.spec 
需要把DvrSocket整个带去去，里面的_internal

a = Analysis(
    ['MainApp.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['TcpServer'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)失效把这个TcpServer添加进去dError: No module named 'TcpServer'  pyinstall

7.29增加二维码识别。但是会一闪而过的过程
运行打包
python .\simple_package.py
