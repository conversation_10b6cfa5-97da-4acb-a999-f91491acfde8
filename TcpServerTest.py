import logging
import socket
import threading
import json
from typing import Callable, Optional
import Crawler
from Result import Result


class TCPServer:
    """TCP服务端类"""

    def __init__(self, logger, iniconfig, host: str = '0.0.0.0', port: int = 8888):
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        self.clients = {}  # {client_socket: (ip, port)}
        self.logger = logger
        self.config = iniconfig
        self.crawler = None

    def _handle_client(self, client_socket: socket.socket, addr: tuple) -> None:
        """处理单个客户端连接"""
        self.clients[client_socket] = addr
        print(f"[Server] Client {addr} connected.")
        self.logger.info(f"[Server] Client {addr} connected.")

        try:
            while self.running:
                data = client_socket.recv(1024).decode('utf-8')
                if not data:
                    break
                print(f"[Server] Received from {addr}: {data}")
                self.logger.info(f"[Server] Received from {addr}: {data}")
                # json格式的
                # {
                #     "command": "qr_code",
                #     "ConfigVariant":
                #         {
                #             "WLAN": "240075264437"
                #         }
                #
                # }
                # 消息分发处理
                # data.replace('\r\r\n', '\n').strip()

                response = self.handle_command(data)

                #
                # 发送响应（示例：原样返回）
                # if isinstance(response, str):
                #     response = response
                if isinstance(response, dict):
                    response = json.dumps(response)
                if isinstance(response, Result):
                    response = response.to_json()

                self.logger.info(f"[Server] Send to {addr}: {response}")

                client_socket.sendall(response.encode("utf-8"))
                # client_socket.sendall(data.encode('utf-8'))
        except (ConnectionResetError, BrokenPipeError):
            print(f"[Server] Client {addr} disconnected unexpectedly.")
            self.logger.error(f"[Server] Client {addr} disconnected unexpectedly.")
        except UnicodeDecodeError as e:
            # 捕获无法解析的数据异常
            print(f"Failed to decode {e}")
            error_message = f"Error: {str(e)}"
            client_socket.sendall(Result.error(error_message).to_json().encode("utf-8"))
            print(f"Sent error message back to server: {error_message}")
        except Exception as e:
            print(f"[Server] Client {addr} disconnected unexpectedly.")
            self.logger.error(f"[Server] Client {addr} disconnected error{str(e)}")
            client_socket.sendall(Result.error(str(e)).to_json().encode("utf-8"))

        finally:
            client_socket.close()
            del self.clients[client_socket]

            print(f"[Server] Client {addr} disconnected.")
            self.logger.info(f"[Server] Client {addr} disconnected.")

    def start(self) -> None:
        """启动服务端"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        self.running = True
        print(f"[Server] Listening on {self.host}:{self.port}")
        self.logger.info(f"[Server] Listening on {self.host}:{self.port}")

        try:
            while self.running:
                client_sock, addr = self.server_socket.accept()
                thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_sock, addr))
                thread.daemon = True
                thread.start()
        except KeyboardInterrupt:
            self.stop()

    def stop(self) -> None:
        """停止服务端"""
        self.running = False
        for client in self.clients.copy():
            client.close()
        self.server_socket.close()
        print("[Server] Stopped.")
        self.logger.info("[Server] Stopped.")

    def get_crawler(self):
        if self.crawler is None:
            self.crawler = Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger)

    # 分发处理
    def handle_command(self, data):
        #
        # self.get_crawler()
        #
        # json格式的
        # {
        #     "command": "qr_code",
        #     "ConfigVariant":
        #         {
        #             "WLAN": "240075264437"
        #         }
        #
        # }
        # 解析输入参数
        try:
            json_data = json.loads(data)
            # 检查json格式
            self.check_json_structure(json_data)
            # 指令
            command = json_data["command"]
            # 参数
            configVariant = json_data["ConfigVariant"]
            #
            # 系统参数页面 信息页面
            # {
            #     "command": "qr_code",
            #     "ConfigVariant":
            #         {
            #             "WLAN": "240075264437"
            #         }
            #
            # }
            if command == "Imei":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "appVer":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "sysPower":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "acc":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "AiVersion":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "Imsi":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "Model_4G":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "mcuVer":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            if command == "ExtVer":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # 加速度
            if command == "g_sensor":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # wifi设置页面
            if command == "ssid":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            # 认证模式：1：共享模式，2：WPA, 3: WPA - PSK
            if command == "authMode":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            if command == "pwd":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            # //加密类型 0-NONE 1-TKIP 2AES
            if command == "encType":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            # 终端配置页面
            if command == "devID":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_terminal_setting_info(
                    command=command, configVariant=configVariant)
            # 中心设置，中心服务器设置，现在是取第一个
            # protocolType : 4 还有很多，其他的不判断的了，有算法，ConstantTool-->Protocol_Map 字典
            if command == "protocolType":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            if command == "port":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            if command == "ip":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            if command == "gpsInterval":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            # 拨号设置 APN?
            # apn: 接入点
            if command == "apn":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command,configVariant=configVariant)
            # centerNo: 中心号
            if command == "centerNo":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command,configVariant=configVariant)
            # user: 用户名
            if command == "user":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command,configVariant=configVariant)
            # password: 密码
            if command == "password":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command,configVariant=configVariant)
            # 下载图片
            if command == "qr_code":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).download_qr_code(command=command,
                                                                                                       configVariant=configVariant
                                                                                                       )
            # 网络信息
            #  设置ITNO:1;tongu1:2;
            # SIM信号
            if command == "simSignal":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_net_info(
                    command=command,configVariant=configVariant)
            # SIM存在
            if command == "simStatus":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_net_info(
                    command=command,configVariant=configVariant)
            # 磁盘信息(TF卡）
            # type: 5:SD1
            # 多少个类型其他匹配看txt文件
            if command == "type":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command,configVariant=configVariant)
            # free：剩余容量
            if command == "free":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command,configVariant=configVariant)
            # size：总容量
            if command == "size":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command,configVariant=configVariant)
            # 状态 1正常，0异常
            if command == "status":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command,configVariant=configVariant)
            # 卫星信息
            # 获取gps
            if command == "gps":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_moon_info(
                    command=command,configVariant=configVariant)
            # 北斗
            if command == "bd":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_moon_info(
                    command=command,configVariant=configVariant)
            # 系统时钟
            # 时区
            if command == "timezone":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_clock_info(
                    command=command,configVariant=configVariant)
            # 串口页面 串口获取
            if command == "serial_test":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_serial_test_info(
                    command=command,configVariant=configVariant)
            # 设置
            if command == "serial_test_start":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_serial_test_set(command=command,configVariant=configVariant)
            else:
                # 指令不对或者没能识别
                return Result.error(msg=f"未能识别你的指令：{command}")

        except UnicodeDecodeError as e:
            error_msg = f"Invalid encoding: {str(e)}"
            self.logger.warning(error_msg)
            error_msg = Result.error(msg=error_msg)
            return error_msg
        except json.JSONDecodeError as e:
            error_msg = f"JSON parse error: {str(e)}"
            self.logger.warning(error_msg)
            error_msg = Result.error(msg="传递的JSON格式有误")
            return error_msg
        except ValueError as e:
            # self.logger.error(f"{e.args[0]}")
            error_msg = f"{e.args[0]}"
            error_msg = Result.error(msg=error_msg)
            return error_msg

        # 登录超时
        except Exception as e:
            self.logger.error(f"{e.args[0]}")
            error_msg = f"{e.args[0]}"
            error_msg = Result.error(msg=error_msg)
            return error_msg

    #
    # 检查是否为正确的输入格式
    def check_json_structure(self, json_obj):
        expected_keys = {"command", "ConfigVariant"}
        if not isinstance(json_obj, dict):
            self.logger.error("Received data is not a dictionary.")
            raise ValueError(f"传递的JSON格式有误")
        if expected_keys == json_obj.keys():
            if isinstance(json_obj["command"], str) and isinstance(json_obj["ConfigVariant"], dict):
                return True
            else:
                self.logger.error(f"Data types do not match expected types.")
                raise ValueError(f"传递的JSON格式有误")
        else:
            self.logger.error("Missing or extra keys in JSON.")
            raise ValueError(f"传递的JSON格式有误")


class TCPClient:
    """TCP客户端类"""

    def __init__(self):
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.connected = False

    def connect(self, host: str = '127.0.0.1', port: int = 8888) -> bool:
        """连接到服务器"""
        try:
            self.sock.connect((host, port))
            self.connected = True
            print(f"[Client] Connected to {host}:{port}")
            return True
        except ConnectionRefusedError:
            print("[Client] Connection refused.")
            return False

    def send(self, message: str) -> Optional[str]:
        """发送消息并获取响应"""
        if not self.connected:
            print("[Client] Not connected!")
            return None
        try:
            self.sock.sendall(message.encode('utf-8'))
            response = self.sock.recv(1024).decode('utf-8')
            return response
        except (ConnectionResetError, BrokenPipeError):
            print("[Client] Connection lost.")
            self.connected = False
            return None

    def disconnect(self) -> None:
        """断开连接"""
        self.sock.close()
        self.connected = False
        print("[Client] Disconnected.")


if __name__ == '__main__':

    server = TCPServer(host='127.0.0.1', port=8066)
    try:
        server.start()
    except KeyboardInterrupt:
        server.stop()
