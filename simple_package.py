#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版打包脚本，不修改源码，专注于打包应用
包括正确处理playwright浏览器驱动
"""

import os
import sys
import shutil
import subprocess
import site
import glob
import tempfile
import zipfile

# 移除install_zxing函数
# 移除copy_java_files函数

def find_pyzbar_dlls():
    """查找pyzbar库所需的DLL文件"""
    print("查找pyzbar库所需的DLL文件...")
    dlls = []
    
    # 尝试在site-packages中查找pyzbar
    for site_dir in site.getsitepackages():
        pyzbar_dir = os.path.join(site_dir, "pyzbar")
        if os.path.exists(pyzbar_dir):
            print(f"找到pyzbar目录: {pyzbar_dir}")
            # 查找DLL文件
            dll_files = glob.glob(os.path.join(pyzbar_dir, "*.dll"))
            if dll_files:
                dlls.extend(dll_files)
                print(f"找到 {len(dll_files)} 个DLL文件")
            else:
                print("在pyzbar目录中没有找到DLL文件")
    
    # 如果没有找到，尝试在系统路径中查找
    if not dlls:
        print("尝试在系统路径中查找所需DLL...")
        for path in os.environ["PATH"].split(os.pathsep):
            for dll_name in ["libiconv.dll", "libzbar.dll"]:
                dll_path = os.path.join(path, dll_name)
                if os.path.exists(dll_path):
                    dlls.append(dll_path)
                    print(f"找到DLL: {dll_path}")
    
    if not dlls:
        print("警告: 未找到pyzbar所需的DLL文件，打包可能会失败")
    
    return dlls

def clean_dist_dir():
    """清理dist目录"""
    dist_dir = os.path.join("dist", "DvrSocker")
    if os.path.exists(dist_dir):
        try:
            shutil.rmtree(dist_dir)
            print(f"✓ 已清理目录: {dist_dir}")
        except Exception as e:
            print(f"! 清理目录失败: {dist_dir}, 错误: {str(e)}")
            return False
    
    # 确保dist目录存在
    dist_parent = os.path.join("dist")
    if not os.path.exists(dist_parent):
        os.makedirs(dist_parent, exist_ok=True)
    
    return True

def check_playwright_dir():
    """检查ms-playwright目录是否存在"""
    if not os.path.exists("ms-playwright"):
        print("! ms-playwright目录不存在")
        return False
    
    print(f"✓ 找到ms-playwright目录")
    return True

def copy_playwright_dir():
    """直接复制ms-playwright目录到打包目录"""
    src_dir = "ms-playwright"
    dst_dir = os.path.join("dist", "DvrSocker", "ms-playwright")
    
    if not os.path.exists(src_dir):
        print(f"! ms-playwright目录不存在: {src_dir}")
        return False
    
    if not os.path.exists(os.path.join("dist", "DvrSocker")):
        print(f"! 打包目录不存在，无法复制ms-playwright")
        return False
    
    # 如果目标目录已存在，先删除
    if os.path.exists(dst_dir):
        try:
            shutil.rmtree(dst_dir)
            print(f"✓ 已清理旧的ms-playwright目录")
        except Exception as e:
            print(f"! 清理ms-playwright目录失败: {str(e)}")
            return False
    
    # 复制目录
    try:
        shutil.copytree(src_dir, dst_dir)
        print(f"✓ 已直接复制ms-playwright目录到打包目录")
        return True
    except Exception as e:
        print(f"× 复制ms-playwright目录失败: {str(e)}")
        return False

def copy_pyzbar_dlls(dlls):
    """复制pyzbar所需的DLL文件到打包目录"""
    if not dlls:
        print("没有找到pyzbar所需的DLL文件，跳过复制")
        return False
    
    # 创建目标目录
    dst_dir = os.path.join("dist", "DvrSocker", "_internal", "pyzbar")
    os.makedirs(dst_dir, exist_ok=True)
    
    # 复制DLL文件
    for dll_path in dlls:
        dll_name = os.path.basename(dll_path)
        dst_path = os.path.join(dst_dir, dll_name)
        try:
            shutil.copy2(dll_path, dst_path)
            print(f"✓ 已复制DLL: {dll_name} 到 {dst_dir}")
        except Exception as e:
            print(f"! 复制DLL失败: {dll_name}, 错误: {str(e)}")
    
    # # 同时复制到根目录，以防万一
    # for dll_path in dlls:
    #     dll_name = os.path.basename(dll_path)
    #     dst_path = os.path.join("dist", "DvrSocker", dll_name)
    #     try:
    #         shutil.copy2(dll_path, dst_path)
    #         print(f"✓ 已复制DLL: {dll_name} 到应用根目录")
    #     except Exception as e:
    #         print(f"! 复制DLL到根目录失败: {dll_name}, 错误: {str(e)}")
    
    return True

def run_pyinstaller():
    """运行PyInstaller打包命令"""
    # 查找pyzbar所需的DLL文件
    pyzbar_dlls = find_pyzbar_dlls()
    
    # 确保ms-playwright目录存在
    if not check_playwright_dir():
        print("! ms-playwright目录不存在，可能导致打包后功能不完整")
    
    # 创建临时目录用于存放DLL
    temp_dir = os.path.join("temp", "pyzbar")
    os.makedirs(temp_dir, exist_ok=True)
    
    # 复制DLL到临时目录
    for dll_path in pyzbar_dlls:
        dll_name = os.path.basename(dll_path)
        dst_path = os.path.join(temp_dir, dll_name)
        try:
            shutil.copy2(dll_path, dst_path)
            print(f"✓ 已复制DLL: {dll_name} 到临时目录")
        except Exception as e:
            print(f"! 复制DLL到临时目录失败: {dll_name}, 错误: {str(e)}")
    
    # 打包命令 - 添加pyzbar的DLL文件
    cmd = [
        "pyinstaller",
        "--icon=app.ico",
        "--windowed",
        "--name=DvrSocker",
        # 添加 -y 选项强制清空输出目录
        "-y",
        # 添加pyzbar所需的DLL
        f"--add-data={temp_dir}{os.pathsep}_internal/pyzbar",
        "MainApp.py"
    ]
    
    print(f"执行打包命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"× 打包失败:\n{result.stderr}")
        return False
    
    # 打包完成后复制ms-playwright目录
    if not copy_playwright_dir():
        print("! ms-playwright目录复制失败，自动化功能可能无法正常工作")
    
    # 额外复制一次pyzbar的DLL文件，确保它们存在于正确位置
    copy_pyzbar_dlls(pyzbar_dlls)
    
    print("✓ 打包成功")
    return True

def create_launcher():
    """创建启动脚本"""
    launcher_path = os.path.join("dist", "DvrSocker", "run.bat")
    
    script_content = """@echo off
REM 启动DvrSocker程序
start "" "%~dp0DvrSocker.exe"
"""
    
    with open(launcher_path, "w") as f:
        f.write(script_content)
    
    print(f"✓ 已创建启动脚本: {launcher_path}")

def create_zip_package():
    """创建ZIP压缩包"""
    print("创建ZIP压缩包...")
    
    app_dir = os.path.join("dist", "DvrSocker")
    if not os.path.exists(app_dir):
        print(f"! 应用目录不存在: {app_dir}")
        return False
    
    zip_path = os.path.join("dist", "DvrSocker.zip")
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(app_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, os.path.dirname(app_dir))
                    zipf.write(file_path, arcname)
        
        print(f"✓ 已创建ZIP压缩包: {zip_path}")
        return True
    except Exception as e:
        print(f"! 创建ZIP压缩包失败: {str(e)}")
        return False

def main():
    print("=== 简化版DvrSocker打包工具 ===")
    
    # 1. 清理dist目录
    if not clean_dist_dir():
        print("! 清理dist目录失败，可能会导致打包错误")
    
    # 2. 运行PyInstaller
    if not run_pyinstaller():
        print("× 打包失败")
        return 1
    
    # 3. 创建ZIP压缩包
    create_zip_package()
    
    print("\n=== 打包完成 ===")
    print("输出目录: dist/DvrSocker")
    print("压缩包: dist/DvrSocker.zip")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 