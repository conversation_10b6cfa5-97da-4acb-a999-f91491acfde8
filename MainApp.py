import datetime
import logging
import socket
from contextlib import suppress
from INIConfigManager import INIConfigManager
import sys, os
from TcpServer import TCPServer

# ----------------------------
# 配置日志（兼容打包后的路径）
# ----------------------------
# log_file = 'sys_%s.log' % datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
# # level：设置日志输出的最低级别，即低于此级别的日志都不会输出
# # 在平时开发测试的时候能够设置成logging.debug以便定位问题，但正式上线后建议设置为logging.WARNING，既能够下降系统I/O的负荷，也能够避免输出过多的无用日志信息
# log_level = logging.WARNING
# # %(module)s - %(funcName)s - %(lineno)d
# # format：设置日志的字符串输出格式
# # log_format = '%(asctime)s[%(levelname)s]: %(message)s'
# log_format='%(asctime)s[%(levelname)s]:%(module)s:%(funcName)s: %(message)s'
# # log_format = '%(asctime):%(module)s:%(funcName)s[%(levelname)s]: %(message)s'


CONFIG_FILE = "DvrSockerConfig.ini"


def setup_logger(base_path):
    log_dir = os.path.join(base_path, "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = 'sys_%s.log' % datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
    log_file = os.path.join(log_dir, log_file)
    # level：设置日志输出的最低级别，即低于此级别的日志都不会输出
    # 在平时开发测试的时候能够设置成logging.debug以便定位问题，但正式上线后建议设置为logging.WARNING，既能够下降系统I/O的负荷，也能够避免输出过多的无用日志信息
    log_level = logging.WARNING
    # format：设置日志的字符串输出格式
    # log_format = '%(asctime)s[%(levelname)s]: %(message)s'
    log_format = '%(asctime)s[%(levelname)s]:%(module)s:%(funcName)s: %(message)s'
    # log_format = '%(asctime):%(module)s:%(funcName)s[%(levelname)s]: %(message)s'

    logging.basicConfig(filename=log_file, level=logging.INFO, format=log_format)

    # logging.basicConfig(filename=log_file, level=logging.INFO, format=log_format)
    logger = logging.getLogger(__name__)
    return logger


def get_local_ip():
    """获取本机真实IPv4地址（排除回环地址）"""
    try:
        # 创建UDP套接字连接到外部地址（不实际发送数据）
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))  # 连接到Google DNS
            return s.getsockname()[0]
    except Exception:
        # 如果上述方法失败，尝试遍历网络接口
        with suppress(Exception):
            hostname = socket.gethostname()
            # 获取所有关联的IP地址
            ip_list = socket.getaddrinfo(hostname, None, socket.AF_INET)
            # 过滤IPv4地址并排除127.0.0.1
            return next(
                (ip[4][0] for ip in ip_list if not ip[4][0].startswith("127.")),
                "127.0.0.1"
            )
        return "0.0.0.0"  # 获取失败时的默认值


if __name__ == '__main__':
    # 获取程序所在目录
    if getattr(sys, 'frozen', False):  # 判断是否由PyInstaller打包
        base_dir = os.path.dirname(sys.executable)  # exe所在目录
    else:
        base_dir = os.path.dirname(os.path.abspath(__file__))  # 脚本所在目录

    # 切换工作目录到exe所在目录
    os.chdir(base_dir)
    #yyy
    logger = setup_logger(base_dir)
    print(logger.handlers)
    # 获取本地ip,写入ini
    local_host = get_local_ip()
    # 获取ini文件的TcpPort,没有就默认8026,获取端口后，
    ini_config = INIConfigManager(CONFIG_FILE)
    port = ini_config.get("MainConfig", "TcpPort", None)
    if not port:
        port = 8026
    ini_config.set("MainConfig", "TcpPort", port, auto_create_section=True)
    ini_config.set("MainConfig", "TcpIp", local_host, auto_create_section=True)

    print("IP地址已成功写入.ini 文件")
    logger.info("IP地址已成功写入.ini 文件")

    server = TCPServer(logger=logger, iniconfig=ini_config, host=local_host, port=port)
    try:
        server.start()
    except KeyboardInterrupt:
        server.stop()
