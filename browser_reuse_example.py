# -*- coding: utf-8 -*-
"""
浏览器复用功能使用示例
"""

from SyncWebTool import (
    open_device_page, 
    open_device_page_async, 
    close_shared_browser, 
    show_shared_browser, 
    hide_shared_browser
)
import time


def example_basic_reuse():
    """基本的浏览器复用示例"""
    print("=== 基本浏览器复用示例 ===")
    
    # 第一次调用会启动浏览器
    print("1. 打开wifi页面 (启动浏览器)")
    open_device_page('wifi', keep_open=2, reuse_browser=True)
    
    # 后续调用会复用同一个浏览器
    print("2. 打开system页面 (复用浏览器)")
    open_device_page('system', keep_open=2, reuse_browser=True)
    
    print("3. 打开network页面 (复用浏览器)")
    open_device_page('network', keep_open=2, reuse_browser=True)
    
    # 手动关闭浏览器
    print("4. 关闭共享浏览器")
    close_shared_browser()


def example_mixed_mode():
    """混合模式示例 - 既有复用也有独立浏览器"""
    print("\n=== 混合模式示例 ===")
    
    # 使用复用模式
    print("1. 使用复用模式打开wifi页面")
    open_device_page('wifi', keep_open=2, reuse_browser=True)
    
    # 使用独立模式（会创建新的浏览器实例）
    print("2. 使用独立模式打开system页面")
    open_device_page('system', keep_open=2, reuse_browser=False)
    
    # 再次使用复用模式（会复用第一个浏览器）
    print("3. 再次使用复用模式打开network页面")
    open_device_page('network', keep_open=2, reuse_browser=True)
    
    # 关闭共享浏览器
    close_shared_browser()


def example_manual_control():
    """手动控制浏览器显示/隐藏示例"""
    print("\n=== 手动控制示例 ===")
    
    # 打开页面
    print("1. 打开wifi页面")
    open_device_page('wifi', keep_open=1, reuse_browser=True)
    
    # 手动隐藏浏览器
    print("2. 手动隐藏浏览器")
    hide_shared_browser()
    time.sleep(2)
    
    # 在后台打开另一个页面
    print("3. 在后台打开system页面")
    open_device_page('system', keep_open=1, reuse_browser=True)
    
    # 显示浏览器
    print("4. 显示浏览器")
    show_shared_browser()
    time.sleep(2)
    
    # 关闭浏览器
    close_shared_browser()


def example_async_reuse():
    """异步函数的浏览器复用示例"""
    print("\n=== 异步函数复用示例 ===")
    
    # 使用异步函数进行浏览器复用
    print("1. 异步打开wifi页面")
    open_device_page_async('wifi', keep_open=2, auto_close=True, reuse_browser=True)
    
    print("2. 异步打开system页面")
    open_device_page_async('system', keep_open=2, auto_close=True, reuse_browser=True)
    
    print("3. 异步打开network页面，不自动关闭")
    open_device_page_async('network', keep_open=2, auto_close=False, reuse_browser=True)
    
    # 手动关闭
    close_shared_browser()


def example_performance_comparison():
    """性能对比示例"""
    print("\n=== 性能对比示例 ===")
    
    pages = ['wifi', 'system', 'network']
    
    # 测试传统模式（每次创建新浏览器）
    print("传统模式 - 每次创建新浏览器:")
    start_time = time.time()
    for page in pages:
        print(f"  打开 {page} 页面")
        open_device_page(page, keep_open=0, reuse_browser=False)
    traditional_time = time.time() - start_time
    print(f"传统模式总耗时: {traditional_time:.2f} 秒")
    
    # 测试复用模式
    print("\n复用模式 - 复用同一个浏览器:")
    start_time = time.time()
    for page in pages:
        print(f"  打开 {page} 页面")
        open_device_page(page, keep_open=0, reuse_browser=True)
    reuse_time = time.time() - start_time
    print(f"复用模式总耗时: {reuse_time:.2f} 秒")
    
    # 关闭浏览器
    close_shared_browser()
    
    # 显示性能提升
    if traditional_time > 0:
        improvement = ((traditional_time - reuse_time) / traditional_time) * 100
        print(f"\n性能提升: {improvement:.1f}%")


if __name__ == "__main__":
    print("浏览器复用功能演示")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_reuse()
    example_mixed_mode()
    example_manual_control()
    example_async_reuse()
    example_performance_comparison()
    
    print("\n演示完成！")
