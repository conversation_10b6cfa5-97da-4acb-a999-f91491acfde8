import logging
import socket
import threading
import json
from typing import Callable, Optional
import Crawler
from Result import Result
from queue import Queue
from concurrent.futures import ThreadPoolExecutor
from SyncWebTool import open_device_page, open_device_page_async

from SyncWebTool import open_device_page

class TCPServer:
    """TCP服务端类"""

    def __init__(self, logger, iniconfig, host: str = '0.0.0.0', port: int = 8888):
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        self.clients = {}  # {client_socket: (ip, port)}
        self.logger = logger
        self.config = iniconfig
        self.crawler = None
        # 增加工作处理耗时
        self.task_queue = Queue()
        self.lock = threading.Lock()
        # 添加线程池用于异步执行浏览器操作
        self.browser_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="BrowserWorker")
        # 存储活跃的浏览器实例，用于远程关闭
        self.active_browsers = {}  # {browser_id: SyncWebTool实例}

    # def _start_workers(self):
    #     """启动4个处理线程"""
    #     for _ in range(4):
    #         t = threading.Thread(target=self._process_tasks, daemon=True)
    #         t.start()
    #
    # def _process_tasks(self):
    #     """任务处理线程"""
    #     while True:
    #         client_id, data = self.task_queue.get()
    #         try:
    #             self._handle_command(client_id, data)
    #         except Exception as e:
    #             self.logger.error(f"处理异常 {client_id}: {str(e)}")
    #         finally:
    #             self.task_queue.task_done()

    def _async_worker(self):
        """异步任务处理线程"""
        while self.running:
            if not self.task_queue.empty():
                conn, data = self.task_queue.get()
                try:
                    # 模拟耗时操作 (替换为实际爬虫逻辑)
                    result = self.handle_command(data)
                    if isinstance(result, str):
                        result = result
                    if isinstance(result, dict):
                        result = json.dumps(result)
                    if isinstance(result, Result):
                        result = result.to_json()

                    # 发送响应

                    response = result.encode('utf-8')
                    # header = struct.pack('!I', len(response))
                    # conn.sendall(header + response)
                    self.logger.info(f"[Server] handle_command {self.clients[conn]} return data: {result}")
                    print(f"[Server] handle_command {self.clients[conn]} return data : {result}")
                    #
                    # with self.lock:
                    conn.sendall(response)
                    self.logger.info(f"[Server] 成功 Send to {self.clients[conn]}: {result}")
                    print(f"[Server] 成功 Send to {self.clients[conn]}: {result}")
                    # conn.send(response)
                except Exception as e:
                    print(f"Process error: {e}")
                    self.logger.error(f"任务线程处理出错data{data}error: {e}")
                    # conn.sendall(Result.error(msg=f"任务线程处理数据错error: {e}").to_json().encode("utf-8"))
                finally:
                    self.task_queue.task_done()

    def _handle_client(self, client_socket: socket.socket, addr: tuple) -> None:
        """处理单个客户端连接"""
        self.clients[client_socket] = addr
        print(f"[Server] Client {addr} connected.")
        self.logger.info(f"[Server] Client {addr} connected.")

        try:
            while self.running:
                data = client_socket.recv(1024).decode('utf-8')
                if not data:
                    break
                print(f"[Server] Received from {addr}: {data}")
                self.logger.info(f"[Server] Received from {addr}: {data}")
                # json格式的
                # {
                #     "command": "qr_code",
                #     "ConfigVariant":
                #         {
                #             "WLAN": "240075264437"
                #         }
                #
                # }
                # 消息分发处理
                # data.replace('\r\r\n', '\n').strip()

                # response = self.handle_command(data)
                # 将耗时操作放入队列异步处理
                self.task_queue.put((client_socket, data))
                # self.logger.info("任务放入任务队列中")

                #
                # 发送响应（示例：原样返回）
                # if isinstance(response, str):
                #     response = response
                # if isinstance(response, dict):
                #     response = json.dumps(response)
                # if isinstance(response, Result):
                #     response = response.to_json()

                # self.logger.info(f"[Server] Send to {addr}: {response}")

                # client_socket.sendall(response.encode("utf-8"))
                # client_socket.sendall(data.encode('utf-8'))
        except (ConnectionResetError, BrokenPipeError):
            print(f"[Server] Client {addr} disconnected unexpectedly.")
            self.logger.error(f"[Server] Client {addr} disconnected unexpectedly.")
        except UnicodeDecodeError as e:
            # 捕获无法解析的数据异常
            print(f"Failed to decode {e}")
            error_message = f"Error: {str(e)}"
            client_socket.sendall(Result.error(error_message).to_json().encode("utf-8"))
            print(f"Sent error message back to server: {error_message}")
        except Exception as e:
            print(f"[Server] Client {addr} disconnected unexpectedly.")
            self.logger.error(f"[Server] Client {addr} disconnected error{str(e)}")
            client_socket.sendall(Result.error(str(e)).to_json().encode("utf-8"))

        finally:
            client_socket.close()
            del self.clients[client_socket]

            print(f"[Server] Client {addr} disconnected.")
            self.logger.info(f"[Server] Client {addr} disconnected.")

    def start(self) -> None:
        """启动服务端"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        self.running = True
        print(f"[Server] Listening on {self.host}:{self.port}")
        self.logger.info(f"[Server] Listening on {self.host}:{self.port}")
        # 启动异步工作线程
        # 开启3条
        for _ in range(1):
            threading.Thread(target=self._async_worker, daemon=True).start()
        try:
            while self.running:
                client_sock, addr = self.server_socket.accept()
                thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_sock, addr))
                thread.daemon = True
                thread.start()
        except KeyboardInterrupt:
            self.stop()

    def stop(self) -> None:
        """停止服务端"""
        self.running = False
        for client in self.clients.copy():
            client.close()
        self.server_socket.close()
        # 等待任务队列处理完成
        self.task_queue.join()
        # 关闭所有活跃的浏览器
        self._close_all_browsers()
        # 关闭线程池
        self.browser_executor.shutdown(wait=False)
        print("[Server] Stopped.")
        self.logger.info("[Server] Stopped.")

    def get_crawler(self):
        if self.crawler is None:
            self.crawler = Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger)

    def _async_open_browser_page(self, page_name, device_ip=None, password=None, keep_open=120, config_variant=None):
        """异步执行浏览器页面打开操作"""
        try:
            if device_ip is None:
                base_url = self.config.get("CrawlerConfing", "BaseUrl", "http://**************")
                device_ip = base_url.replace("http://", "").replace("https://", "")
            if password is None:
                password = self.config.get("CrawlerConfing", "PassWord", "111111", auto_type=False)  # 强制返回字符串

            # 从配置参数中获取自定义设置
            auto_close = True  # 默认自动关闭
            if config_variant:
                #keep_open前端传递的是字符串 请检查参数和类型
                keep_open_val = config_variant.get("keep_open", None)
                try:
                    if keep_open_val and keep_open_val.isdigit():
                        keep_open = int(keep_open_val)
                except Exception as e:
                    self.logger.error(f"[异步任务] keep_open参数类型错误,沿用默认参数: {str(e)}")

                keep_open = keep_open
                device_ip = config_variant.get("device_ip", device_ip)
                password = str(config_variant.get("password", password))  # 确保密码是字符串
                auto_close = config_variant.get("auto_close", True)  # 允许配置是否自动关闭

            # 确保所有参数都是正确的类型
            password = str(password)  # 再次确保密码是字符串
            device_ip = str(device_ip)  # 确保IP是字符串

            self.logger.info(f"[异步任务] 开始执行浏览器页面打开: {page_name}")
            self.logger.info(f"[异步任务] 参数 - IP: {device_ip}, 密码: {password}, 保持时间: {keep_open}秒, 自动关闭: {auto_close}")

            # 使用异步友好的函数
            success = open_device_page_async(page_name, device_ip=device_ip, password=password,
                                           keep_open=keep_open, auto_close=auto_close)

            if success:
                self.logger.info(f"[异步任务] 浏览器页面 {page_name} 操作成功完成")
            else:
                self.logger.error(f"[异步任务] 浏览器页面 {page_name} 操作失败")

            return success
        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            self.logger.error(f"[异步任务] 异步打开浏览器页面失败: {page_name}")
            self.logger.error(f"[异步任务] 错误详情: {str(e)}")
            self.logger.error(f"[异步任务] 堆栈跟踪: {error_detail}")
            return False

    def _close_browser_by_command(self):
        """通过命令关闭由本应用程序打开的浏览器窗口"""
        try:
            # 尝试使用psutil进行精确识别和关闭
            try:
                import psutil
                
                # 查找并关闭Chrome/Chromium进程
                closed_count = 0
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        # 检查是否是浏览器进程
                        if proc.info['name'] and any(browser in proc.info['name'].lower()
                                                   for browser in ['chrome', 'chromium', 'msedge']):
                            # 检查命令行参数，确保是我们启动的浏览器
                            cmdline = proc.info['cmdline'] or []
                            # 我们的程序启动浏览器时使用了特定参数
                            if any('--no-sandbox' in arg for arg in cmdline) and any('--disable-dev-shm-usage' in arg for arg in cmdline):
                                self.logger.info(f"[浏览器关闭] 终止浏览器进程: PID={proc.info['pid']}, 名称={proc.info['name']}")
                                proc.terminate()
                                closed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue

                if closed_count > 0:
                    self.logger.info(f"[浏览器关闭] 成功关闭 {closed_count} 个浏览器进程")
                    return True
                else:
                    self.logger.info(f"[浏览器关闭] 没有找到需要关闭的浏览器进程")
            
            except ImportError:
                self.logger.warning("[浏览器关闭] psutil未安装，将使用备用方法")
            
            # 备用方法：使用Windows特定命令查找和关闭浏览器
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                # 获取设备IP，用于标识窗口
                base_url = self.config.get("CrawlerConfing", "BaseUrl", "")
                device_ip = base_url.replace("http://", "").replace("https://", "")
                
                # 使用wmic查找带有特定参数的Chrome进程
                try:
                    # 查找命令行中包含no-sandbox和disable-dev-shm-usage的Chrome进程
                    find_cmd = 'wmic process where "name=\'chrome.exe\' and commandline like \'%no-sandbox%\' and commandline like \'%disable-dev-shm-usage%\'" get processid'
                    result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                    
                    # 解析进程ID
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:  # 第一行是标题"ProcessId"
                        pids = [line.strip() for line in lines[1:] if line.strip()]
                        
                        # 关闭找到的进程
                        for pid in pids:
                            try:
                                subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                                self.logger.info(f"[浏览器关闭] 终止Chrome进程: PID={pid}")
                            except:
                                pass
                        
                        self.logger.info(f"[浏览器关闭] 成功关闭 {len(pids)} 个Chrome进程")
                        return True
                except Exception as e:
                    self.logger.error(f"[浏览器关闭] wmic查询失败: {str(e)}")
                
                # 如果wmic方法失败，尝试使用tasklist和findstr
                try:
                    # 查找Chrome进程
                    find_cmd = 'tasklist /FI "IMAGENAME eq chrome.exe" /FO CSV'
                    result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                    
                    if "chrome.exe" in result.stdout:
                        # 找到Chrome进程，使用特定参数关闭
                        kill_cmd = 'taskkill /F /FI "WINDOWTITLE eq *no-sandbox*" /IM chrome.exe'
                        subprocess.run(kill_cmd, shell=True)
                        
                        # 也可能是Edge浏览器
                        kill_edge_cmd = 'taskkill /F /FI "WINDOWTITLE eq *no-sandbox*" /IM msedge.exe'
                        subprocess.run(kill_edge_cmd, shell=True)
                        
                        self.logger.info("[浏览器关闭] 使用taskkill关闭浏览器")
                        return True
                except Exception as e:
                    self.logger.error(f"[浏览器关闭] tasklist/taskkill失败: {str(e)}")
            
            # 如果所有方法都失败，使用最基本的方法
            self.logger.warning("[浏览器关闭] 使用基本方法关闭浏览器")
            try:
                # 在Windows上使用更安全的参数
                if platform.system() == "Windows":
                    # 尝试使用window title过滤
                    subprocess.run(['taskkill', '/F', '/FI', 'WINDOWTITLE eq *no-sandbox*', '/IM', 'chrome.exe'], 
                                 capture_output=True, text=True)
                    subprocess.run(['taskkill', '/F', '/FI', 'WINDOWTITLE eq *no-sandbox*', '/IM', 'msedge.exe'], 
                                 capture_output=True, text=True)
                else:
                    # Linux/Mac系统
                    subprocess.run(['pkill', '-f', 'chrome.*no-sandbox.*disable-dev-shm-usage'],
                                 capture_output=True, text=True)
                    subprocess.run(['pkill', '-f', 'chromium.*no-sandbox.*disable-dev-shm-usage'],
                                 capture_output=True, text=True)
                
                return True
            except Exception as e:
                self.logger.error(f"[浏览器关闭] 基本方法失败: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"[浏览器关闭] 关闭浏览器时出错: {str(e)}")
            return False

    # 分发处理
    def handle_command(self, data):
        #
        # self.get_crawler()
        #
        # json格式的
        # {
        #     "command": "qr_code",
        #     "ConfigVariant":
        #         {
        #             "WLAN": "240075264437"
        #         }
        #
        # }
        # 解析输入参数
        try:
            json_data = json.loads(data)
            # 检查json格式
            self.check_json_structure(json_data)
            # 指令
            command = json_data["command"]
            # 参数
            configVariant = json_data["ConfigVariant"]
            #
            # 系统参数页面 信息页面
            # {
            #     "command": "qr_code",
            #     "ConfigVariant":
            #         {
            #             "WLAN": "240075264437"
            #         }
            #
            # }
            # IMEI
            if command == "Imei":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # app版本
            if command == "appVer":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # 系统电压
            if command == "sysPower":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            ##acc状态
            if command == "acc":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # 扩展版本
            if command == "AiVersion":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # IMSI信息
            if command == "Imsi":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # 4G模块
            if command == "Model_4G":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            #
            if command == "mcuVer":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # Ai认证
            if command == "ExtVer":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # sn
            if command == "sn":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # 加速度
            if command == "g_sensor":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_sys_info(command=command,
                                                                                                   configVariant=configVariant)
            # wifi设置页面
            if command == "ssid":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            # 认证模式：1：共享模式，2：WPA, 3: WPA - PSK
            if command == "authMode":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            if command == "pwd":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            # //加密类型 0-NONE 1-TKIP 2AES
            if command == "encType":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            if command == "workMode":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_wifi_setting_info(
                    command=command, configVariant=configVariant)
            # 终端配置页面
            if command == "devID":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_terminal_setting_info(
                    command=command, configVariant=configVariant)
            # 中心设置，中心服务器设置，现在是取第一个
            # protocolType : 4 还有很多，其他的不判断的了，有算法，ConstantTool-->Protocol_Map 字典
            if command == "protocolType":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            if command == "port":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            if command == "ip":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            if command == "gpsInterval":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_center_setting_info(
                    command=command, configVariant=configVariant)
            # 拨号设置 APN?
            # apn: 接入点
            if command == "apn":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command, configVariant=configVariant)
            # centerNo: 中心号
            if command == "centerNo":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command, configVariant=configVariant)
            # user: 用户名
            if command == "user":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command, configVariant=configVariant)
            # password: 密码
            if command == "password":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command, configVariant=configVariant)
            # 鉴权方式
            if command == "auth_type":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_dialup_setting_info(
                    command=command, configVariant=configVariant)
            # 下载图片
            if command == "qr_code":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).download_qr_code(command=command,
                                                                                                       configVariant=configVariant
                                                                                                       )
            # 网络信息
            #  设置ITNO:1;tongu1:2;
            # SIM信号
            if command == "simSignal":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_net_info(
                    command=command, configVariant=configVariant)
            # SIM存在
            if command == "simStatus":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_net_info(
                    command=command, configVariant=configVariant)
            # 磁盘信息(TF卡）
            # type: # 5:SD1
            # 多少个类型其他匹配看txt文件
            if command == "type":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command, configVariant=configVariant)
            # free：剩余容量
            if command == "free":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command, configVariant=configVariant)
            # size：总容量
            if command == "size":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command, configVariant=configVariant)
            # 状态 1正常，0异常
            if command == "status":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_disk_info(
                    command=command, configVariant=configVariant)
            # 卫星信息
            # 获取gps
            if command == "gps":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_moon_info(
                    command=command, configVariant=configVariant)
            # 北斗
            if command == "bd":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_moon_info(
                    command=command, configVariant=configVariant)
            # 系统时钟
            # 时区
            if command == "timezone":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_clock_info(
                    command=command, configVariant=configVariant)
            # 串口页面 串口获取
            if command == "serial_test":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_serial_test_info(
                    command=command, configVariant=configVariant)
            # 设置
            if command == "serial_test_start":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_serial_test_set(
                    command=command, configVariant=configVariant)
            # adas 开始标定 动作
            if command == "adas":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_adas_set(
                    command=command, configVariant=configVariant)
            # ai报警设置adas通道为1
            if command == "built_software":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_built_software_set(
                    command=command, configVariant=configVariant)
            # 获取图片
            if command == "get_img_preview":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_img_preview(
                    command=command, configVariant=configVariant)
            # 设置全通道图片预览
            if command == "all_preview_chn_set":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).all_preview_chn_set(
                    command=command, configVariant=configVariant)
            # 设置单通道预览，通道1,2，
            if command == "preview_chn_set":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).preview_chn_set(
                    command=command, configVariant=configVariant)
            # 设置io报警
            if command == "io_alarm":
                return Crawler.CrawlerFunc(iniconfig=self.config, logger=self.logger).get_io_set(
                    command=command, configVariant=configVariant)

            #ceshi - 异步浏览器页面操作
            if command=="wifi_page":
                # 异步执行浏览器操作，立即返回成功响应，传递配置参数
                self.browser_executor.submit(self._async_open_browser_page, 'wifi', config_variant=configVariant)
                return Result.success(value={"wifi_page":"浏览器将启动打开wifi页面"}, configVariant=configVariant)
            if command =="system_page":
                # 异步执行浏览器操作，立即返回成功响应，传递配置参数
                self.browser_executor.submit(self._async_open_browser_page, 'system', config_variant=configVariant)
                return Result.success(value={"system_page":"浏览器将启动打开系统信息页面"}, configVariant=configVariant)
            if command =="preview_page":
                # 异步执行浏览器操作，立即返回成功响应，传递配置参数
                self.browser_executor.submit(self._async_open_browser_page, 'preview', config_variant=configVariant)
                return Result.success(value={"preview_page":"浏览器将启动打开图片预览页面"}, configVariant=configVariant)

            # 关闭浏览器命令
            if command == "close_browser":
                # 异步执行浏览器关闭操作，立即返回响应
                self.browser_executor.submit(self._close_browser_by_command)
                return Result.success(value={"close_browser":"浏览器将关闭"}, configVariant=configVariant)

            # preview
            else:
                # 指令不对或者没能识别
                return Result.error(msg=f"未能识别你的指令：{command}")

        except UnicodeDecodeError as e:
            error_msg = f"Invalid encoding: {str(e)}"
            self.logger.warning(error_msg)
            error_msg = Result.error(msg=error_msg)
            return error_msg
        except json.JSONDecodeError as e:
            error_msg = f"JSON parse error: {str(e)}"
            self.logger.warning(error_msg)
            error_msg = Result.error(msg="传递的JSON格式有误")
            return error_msg
        except ValueError as e:
            # self.logger.error(f"{e.args[0]}")
            error_msg = f"{e.args[0]}"
            error_msg = Result.error(msg=error_msg)
            return error_msg

        # 登录超时
        except Exception as e:
            self.logger.error(f"{e.args[0]}")
            error_msg = f"{e.args[0]}"
            error_msg = Result.error(msg=error_msg)
            return error_msg

    #
    # 检查是否为正确的输入格式
    def check_json_structure(self, json_obj):
        expected_keys = {"command", "ConfigVariant"}
        if not isinstance(json_obj, dict):
            self.logger.error("Received data is not a dictionary.")
            raise ValueError(f"传递的JSON格式有误")
        if expected_keys == json_obj.keys():
            if isinstance(json_obj["command"], str) and isinstance(json_obj["ConfigVariant"], dict):
                return True
            else:
                self.logger.error(f"Data types do not match expected types.")
                raise ValueError(f"传递的JSON格式有误")
        else:
            self.logger.error("Missing or extra keys in JSON.")
            raise ValueError(f"传递的JSON格式有误")


class TCPClient:
    """TCP客户端类"""

    def __init__(self):
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.connected = False

    def connect(self, host: str = '127.0.0.1', port: int = 8888) -> bool:
        """连接到服务器"""
        try:
            self.sock.connect((host, port))
            self.connected = True
            print(f"[Client] Connected to {host}:{port}")
            return True
        except ConnectionRefusedError:
            print("[Client] Connection refused.")
            return False

    def send(self, message: str) -> Optional[str]:
        """发送消息并获取响应"""
        if not self.connected:
            print("[Client] Not connected!")
            return None
        try:
            self.sock.sendall(message.encode('utf-8'))
            response = self.sock.recv(1024).decode('utf-8')
            return response
        except (ConnectionResetError, BrokenPipeError):
            print("[Client] Connection lost.")
            self.connected = False
            return None

    def disconnect(self) -> None:
        """断开连接"""
        self.sock.close()
        self.connected = False
        print("[Client] Disconnected.")


if __name__ == '__main__':

    server = TCPServer(host='127.0.0.1', port=8026)
    try:
        server.start()
    except KeyboardInterrupt:
        server.stop()
