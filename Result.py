
class Result:
    def __init__(self, code, msg, value={}, configVariant={}):
        """
        初始化返回结果
        :param code: 状态码 (e.g., 200, 400, 500)
        :param message: 消息 (e.g., "Success", "Error")
        :param 返回的数据 (e.g., JSON, list, dict)
        """
        self.code = code
        self.msg = msg
        self.value = value
        self.configVariant = configVariant

    @staticmethod
    def success(value=None, configVariant={}):
        """
        返回成功结果
        :param 返回的数据
        :return: Result对象
        """
        return Result(0, "", value, configVariant)

    @staticmethod
    def error(code=1, msg="", data=None):
        """
        返回错误结果
        :param code: 错误状态码
        :param message: 错误消息
        :param 错误相关的数据
        :return: Result对象
        """
        return Result(code, msg)

    def to_dict(self):
        """
        将Result对象转换为字典
        :return: 字典格式的返回结果
        """
        return {
            "code": self.code,
            "msg": self.msg,
            "ConfigVariant": self.configVariant,
            "Value": self.value
        }

    def to_json(self):
        """
        将Result对象转换为JSON字符串
        :return: JSON格式的返回结果
        """
        import json
        return json.dumps(self.to_dict(), ensure_ascii=False)


