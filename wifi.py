import subprocess
import os
import sys
import time
import logging
from argparse import ArgumentParser
import configparser

# 此为打包成exe供qt自动连接wifi
# 打包命令
# pyinstaller  --windowed --name wifi wifi.py
#pyinstaller .\wifi.spec

# === 配置文件路径 ===
CONFIG_FILE = "DvrSockerConfig.ini"

logger = logging.getLogger(__name__)


# === 配置文件管理 ===
def read_config() -> tuple:
    """读取配置文件，返回 (ssid, password)"""
    config = configparser.ConfigParser()
    if not os.path.exists(CONFIG_FILE):
        return None, None

    try:
        config.read(CONFIG_FILE)
        ssid = config.get("MainConfig", "SSID", fallback=None)
        password = config.get("MainConfig", "PWD", fallback=None)
        return ssid, password
    except Exception as e:
        logger.error(f"读取配置文件失败: {str(e)}")
        return None, None


# === 核心连接逻辑 ===
def connect_to_wifi(ssid: str, password: str, max_retries: int = 3, retry_interval: int = 10):
    profile_name = f"{ssid}.xml"
    profile_content = f'''<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
  <name>{ssid}</name>
  <SSIDConfig>
    <SSID>
      <name>{ssid}</name>
    </SSID>
  </SSIDConfig>
  <connectionType>ESS</connectionType>
  <connectionMode>auto</connectionMode>
  <MSM>
    <security>
      <authEncryption>
        <authentication>WPA2PSK</authentication>
        <encryption>AES</encryption>
        <useOneX>false</useOneX>
      </authEncryption>
      <sharedKey>
        <keyType>passPhrase</keyType>
        <protected>false</protected>
        <keyMaterial>{password}</keyMaterial>
      </sharedKey>
    </security>
  </MSM>
</WLANProfile>'''

    for attempt in range(1, max_retries + 1):
        try:
            logger.info(f"尝试连接 {ssid} (第 {attempt} 次重试)...")

            # 生成临时配置文件
            with open(profile_name, "w") as f:
                f.write(profile_content)

            # 添加配置文件并连接
            subprocess.run(
                ["netsh", "wlan", "add", "profile", f"filename={profile_name}"],
                check=True,
                shell=True,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            subprocess.run(
                ["netsh", "wlan", "connect", f"name={ssid}"],
                check=True,
                shell=True,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            logger.info(f"成功连接到 {ssid}!")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"连接失败: {str(e)}")
            time.sleep(retry_interval)
        finally:
            if os.path.exists(profile_name):
                os.remove(profile_name)

    logger.error(f"超过最大重试次数 ({max_retries})，放弃连接")
    return False


# === 管理员权限检查 ===
def is_admin():
    try:
        return subprocess.check_output(["net", "session"], shell=True, stderr=subprocess.DEVNULL)
    except subprocess.CalledProcessError:
        return False


# === 命令行模式 ===
def cli_mode():
    parser = ArgumentParser(description="自动连接 WiFi")
    parser.add_argument("--ssid", help="WiFi 名称")
    parser.add_argument("--password", help="WiFi 密码")
    parser.add_argument("--retries", type=int, default=3, help="最大重试次数")
    parser.add_argument("--no-config", action="store_true", help="忽略已保存的配置")
    args = parser.parse_args()

    # 优先级：命令行参数 > 配置文件 > GUI
    if args.ssid and args.password:
        connect_to_wifi(args.ssid, args.password, args.retries)
    elif not args.no_config:
        saved_ssid, saved_password = read_config()
        if saved_ssid and saved_password:
            logger.info(f"使用配置文件自动连接: {saved_ssid}")
            connect_to_wifi(saved_ssid, saved_password, args.retries)
        else:
            logger.error("未提供参数且未找到配置文件")
            sys.exit(1)
    else:
        logger.error("必须提供 --ssid 和 --password 参数")
        sys.exit(1)


# === GUI 模式 ===
# def gui_mode():
#     try:
#         import tkinter as tk
#         from tkinter import simpledialog, messagebox
#     except ImportError:
#         logger.error("需要安装 tkinter 以使用 GUI 模式")
#         return
#
#     root = tk.Tk()
#     root.withdraw()  # 隐藏主窗口
#
#     ssid = simpledialog.askstring("输入", "请输入 WiFi 名称 (SSID):", initialvalue="240075264437")
#     password = simpledialog.askstring("输入", "请输入 WiFi 密码:", initialvalue="88888888")
#
#     if ssid and password:
#         success = connect_to_wifi(ssid, password)
#         if success:
#             messagebox.showinfo("成功", f"已连接到 {ssid}!")
#         else:
#             messagebox.showerror("失败", f"无法连接 {ssid}!")
#     else:
#         messagebox.showwarning("错误", "必须输入 SSID 和密码")


# === 主入口 ===
if __name__ == "__main__":
    # 获取程序所在目录
    if getattr(sys, 'frozen', False):  # 判断是否由PyInstaller打包
        base_dir = os.path.dirname(sys.executable)  # exe所在目录
    else:
        base_dir = os.path.dirname(os.path.abspath(__file__))  # 脚本所在目录

    # 切换工作目录到exe所在目录
    os.chdir(base_dir)
    # === 日志配置 ===
    log_dir = os.path.join(base_dir, "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    log_file = os.path.join(log_dir, "wifi.log")
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    # if not is_admin():
    #     logger.error("请以管理员身份运行此脚本！")
    #     sys.exit(1)

    cli_mode()
    # if len(sys.argv) > 1:
    #     cli_mode()
    # else:
    #     gui_mode()
