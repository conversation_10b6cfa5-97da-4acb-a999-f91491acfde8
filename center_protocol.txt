this.server_t = "中心服务器设置";
	this.server1_t = "中心服务器1";
	this.server2_t = "中心服务器2";
	this.server3_t = "中心服务器3";
	this.server4_t = "中心服务器4";
	this.server5_t = "中心服务器5";
	this.server6_t = "中心服务器6";
	this.server7_t = "中心服务器7";
	this.server8_t = "中心服务器8";
	this.gps_interval_t = "GPS间隔";
	this.server_ip_t = "IP地址";
	this.server_port_t = "端口";
	this.ext_server_t = "FTP设置";
	this.label_28181 = "28181 设置";
	this.protocol_ttx = "CMSV6";
	this.protocol_rkx  = "R标准";
	this.protocol_hw   = "H协议";
	this.protocol_808  = "部标2013_M1";
	this.protocol_transptone  = "第三方透传1";
	this.protocol_transpttwo  = "第三方透传2";
	this.protocol_8082  = "部标2013_M2";
    this.protocol_8083 = "部标2013_M3";
	this.protocol_hwtwo  = "H协议2";
	this.protocol_transptthree  = "第三方透传3";
	this.protocol_28181 = "GB28181";
	this.protocol_transptfour = "第三方透传4";
	this.protocol_m1 = "部标2019_M1";
	this.protocol_m2  = "部标2019_M2";
	this.protocol_m3  = "部标2019_M3";
	this.protocol_m4 = "部标2019_M4";
	this.protocol_s1  = "部标2019_S1";
	this.protocol_s2  = "部标2019_S2";
	this.protocol_s3 = "部标2019_S3";
	this.protocol_905_2014 = "905_2014";
	this.protocol_TIANJIN_808 = "天津808";
	this.protocol_TTX_2 = "CMSV6-2";
	this.protocol_CHUANGBIAO_209 = "川标2019";
	this.server_parameter_gps_error = "GPS间隔不可为0";
	this.server_parameter_ip_error = "IP地址不可为0";
	this.server_parameter_port_error = "端口不可为0";
	this.protocol_8085 = "部标2013_M5";
	this.protocol_8086 = "部标2013_M6";
	this.protocol_8087 = "部标2013_M7";
	this.protocol_8088 = "部标2013_M8";
	this.protocol_m5 = "部标2019_M5";
	this.protocol_m6 = "部标2019_M6";
	this.protocol_m7 = "部标2019_M7";
	this.protocol_m8 = "部标2019_M8";
	this.protocol_s5 = "部标2019_S5";
	this.protocol_s6 = "部标2019_S6";
	this.protocol_s7 = "部标2019_S7";
	this.protocol_s8 = "部标2019_S8";
	this.protocol_huoyun = "货运平台";
	this.s_server_number_t  = "服务器编号";
	this.s_server_domain_name_t = "服务器域名";
	this.s_device_id_t = "设备编号";
	this.s_login_password_t = "注册密码";
	this.s_channel_number_t = "通道编号";
	this.s_alarm_number_t = "报警编号";
	this.s_registration_period_t = "注册有效期";
	this.s_heartbeat_cycle_t = "心跳周期";
	this.s_connection_type_t = "连接类型";
	this.center_save_error_lock_ip = "中心参数已加锁,无法保存参数";

function initServerSettings(){

	if(serverNum == 1){
		//server1协议
		$("#server_protocol_v").append("<option value='0'>" + getString("close") + "</option>");
		if(mycars[1] > 0) $("#server_protocol_v").append("<option value='1'>" + getString("protocol_ttx") + "</option>"); //T标准
		if(mycars[2]> 0)$("#server_protocol_v").append("<option value='2'>" + getString("protocol_rkx") + "</option>");
		if(mycars[3]> 0)$("#server_protocol_v").append("<option value='3'>" + getString("protocol_hw") + "</option>");
		if(mycars[4]> 0)$("#server_protocol_v").append("<option value='4'>" + getString("protocol_808") + "</option>");
		if(mycars[5]> 0)$("#server_protocol_v").append("<option value='5'>" + getString("protocol_transptone") + "</option>");

		if(mycars[6]> 0)$("#server_protocol_v").append("<option value='6'>" + getString("protocol_transpttwo") + "</option>");
		if(mycars[7]> 0)$("#server_protocol_v").append("<option value='7'>" + getString("protocol_8082") + "</option>");
		if(mycars[8]> 0)$("#server_protocol_v").append("<option value='8'>" + getString("protocol_8083") + "</option>");

		if(mycars[9]> 0)$("#server_protocol_v").append("<option value='9'>" + getString("protocol_hwtwo") + "</option>");
		if(mycars[10]> 0)$("#server_protocol_v").append("<option value='10'>" + getString("protocol_transptthree") + "</option>");
		if(mycars[11]> 0)$("#server_protocol_v").append("<option value='11'>" + getString("protocol_28181") + "</option>");

		if(mycars[12]> 0)$("#server_protocol_v").append("<option value='12'>" + getString("protocol_transptfour") + "</option>");
		if(mycars[13]> 0)$("#server_protocol_v").append("<option value='13'>" + getString("protocol_m1") + "</option>");
		if(mycars[14]> 0)$("#server_protocol_v").append("<option value='14'>" + getString("protocol_m2") + "</option>");

		if(mycars[15]> 0)$("#server_protocol_v").append("<option value='15'>" + getString("protocol_m3") + "</option>");
		if(mycars[16]> 0)$("#server_protocol_v").append("<option value='16'>" + getString("protocol_m4") + "</option>");
		if(mycars[17]> 0)$("#server_protocol_v").append("<option value='17'>" + getString("protocol_s1") + "</option>");
		if(mycars[18]> 0)$("#server_protocol_v").append("<option value='18'>" + getString("protocol_s2") + "</option>");
		if(mycars[19]> 0)$("#server_protocol_v").append("<option value='19'>" + getString("protocol_s3") + "</option>");
		if(mycars[27]> 0)$("#server_protocol_v").append("<option value='27'>" + getString("protocol_huoyun") + "</option>");

		if(mycars[31]> 0)$("#server_protocol_v").append("<option value='31'>" + getString("protocol_905_2014") + "</option>");
		if(mycars[36]> 0)$("#server_protocol_v").append("<option value='36'>" + getString("protocol_TTX_2") + "</option>"); // CMSV6-2  bmlin  230928
		if(mycars[37]> 0)$("#server_protocol_v").append("<option value='37'>" + getString("protocol_TIANJIN_808") + "</option>");	// ���� ���808  bmlin  230815
		if(mycars[38]> 0)$("#server_protocol_v").append("<option value='38'>" + getString("protocol_CHUANGBIAO_209") + "</option>");	// ����  bmlin  230815
	}else if(serverNum == 2){
		//server2协议
		$("#server_protocol_v").append("<option value='0'>" + getString("close") + "</option>");
		if(mycars[1] > 0) $("#server_protocol_v").append("<option value='1'>" + getString("protocol_ttx") + "</option>"); //T标准
		if(mycars[2]> 0)$("#server_protocol_v").append("<option value='2'>" + getString("protocol_rkx") + "</option>");
		if(mycars[3]> 0)$("#server_protocol_v").append("<option value='3'>" + getString("protocol_hw") + "</option>");
		if(mycars[4]> 0)$("#server_protocol_v").append("<option value='4'>" + getString("protocol_808") + "</option>");
		if(mycars[5]> 0)$("#server_protocol_v").append("<option value='5'>" + getString("protocol_transptone") + "</option>");

		if(mycars[6]> 0)$("#server_protocol_v").append("<option value='6'>" + getString("protocol_transpttwo") + "</option>");
		if(mycars[7]> 0)$("#server_protocol_v").append("<option value='7'>" + getString("protocol_8082") + "</option>");
		if(mycars[8]> 0)$("#server_protocol_v").append("<option value='8'>" + getString("protocol_8083") + "</option>");

		if(mycars[9]> 0)$("#server_protocol_v").append("<option value='9'>" + getString("protocol_hwtwo") + "</option>");
		if(mycars[10]> 0)$("#server_protocol_v").append("<option value='10'>" + getString("protocol_transptthree") + "</option>");
		if(mycars[11]> 0)$("#server_protocol_v").append("<option value='11'>" + getString("protocol_28181") + "</option>");

		if(mycars[12]> 0)$("#server_protocol_v").append("<option value='12'>" + getString("protocol_transptfour") + "</option>");
		if(mycars[13]> 0)$("#server_protocol_v").append("<option value='13'>" + getString("protocol_m1") + "</option>");
		if(mycars[14]> 0)$("#server_protocol_v").append("<option value='14'>" + getString("protocol_m2") + "</option>");

		if(mycars[15]> 0)$("#server_protocol_v").append("<option value='15'>" + getString("protocol_m3") + "</option>");
		if(mycars[16]> 0)$("#server_protocol_v").append("<option value='16'>" + getString("protocol_m4") + "</option>");
		if(mycars[17]> 0)$("#server_protocol_v").append("<option value='17'>" + getString("protocol_s1") + "</option>");
		if(mycars[18]> 0)$("#server_protocol_v").append("<option value='18'>" + getString("protocol_s2") + "</option>");
		if(mycars[19]> 0)$("#server_protocol_v").append("<option value='19'>" + getString("protocol_s3") + "</option>");
		if(mycars[31]> 0)$("#server_protocol_v").append("<option value='31'>" + getString("protocol_905_2014") + "</option>");
		if(mycars[37]> 0)$("#server_protocol_v").append("<option value='37'>" + getString("protocol_TIANJIN_808") + "</option>");	// ���� ���808  bmlin  230815
		if(mycars[36]> 0)$("#server_protocol_v").append("<option value='36'>" + getString("protocol_TTX_2") + "</option>"); // CMSV6-2  bmlin  230928

	}else if(serverNum == 3){
		//server3协议
		$("#server_protocol_v").append("<option value='0'>" + getString("close") + "</option>");
		if(mycars[1] > 0) $("#server_protocol_v").append("<option value='1'>" + getString("protocol_ttx") + "</option>"); //T标准
		if(mycars[2]> 0)$("#server_protocol_v").append("<option value='2'>" + getString("protocol_rkx") + "</option>");
		if(mycars[3]> 0)$("#server_protocol_v").append("<option value='3'>" + getString("protocol_hw") + "</option>");
		if(mycars[4]> 0)$("#server_protocol_v").append("<option value='4'>" + getString("protocol_808") + "</option>");
		if(mycars[5]> 0)$("#server_protocol_v").append("<option value='5'>" + getString("protocol_transptone") + "</option>");

		if(mycars[6]> 0)$("#server_protocol_v").append("<option value='6'>" + getString("protocol_transpttwo") + "</option>");
		if(mycars[7]> 0)$("#server_protocol_v").append("<option value='7'>" + getString("protocol_8082") + "</option>");
		if(mycars[8]> 0)$("#server_protocol_v").append("<option value='8'>" + getString("protocol_8083") + "</option>");

		if(mycars[9]> 0)$("#server_protocol_v").append("<option value='9'>" + getString("protocol_hwtwo") + "</option>");
		if(mycars[10]> 0)$("#server_protocol_v").append("<option value='10'>" + getString("protocol_transptthree") + "</option>");
		if(mycars[11]> 0)$("#server_protocol_v").append("<option value='11'>" + getString("protocol_28181") + "</option>");

		if(mycars[12]> 0)$("#server_protocol_v").append("<option value='12'>" + getString("protocol_transptfour") + "</option>");
		if(mycars[13]> 0)$("#server_protocol_v").append("<option value='13'>" + getString("protocol_m1") + "</option>");
		if(mycars[14]> 0)$("#server_protocol_v").append("<option value='14'>" + getString("protocol_m2") + "</option>");

		if(mycars[15]> 0)$("#server_protocol_v").append("<option value='15'>" + getString("protocol_m3") + "</option>");
		if(mycars[16]> 0)$("#server_protocol_v").append("<option value='16'>" + getString("protocol_m4") + "</option>");
		if(mycars[17]> 0)$("#server_protocol_v").append("<option value='17'>" + getString("protocol_s1") + "</option>");
		if(mycars[18]> 0)$("#server_protocol_v").append("<option value='18'>" + getString("protocol_s2") + "</option>");
		if(mycars[19]> 0)$("#server_protocol_v").append("<option value='19'>" + getString("protocol_s3") + "</option>");
		if(mycars[31]> 0)$("#server_protocol_v").append("<option value='31'>" + getString("protocol_905_2014") + "</option>");
		if(mycars[37]> 0)$("#server_protocol_v").append("<option value='37'>" + getString("protocol_TIANJIN_808") + "</option>"); // ���808  ����  bmlin  230815
		if(mycars[36]> 0)$("#server_protocol_v").append("<option value='36'>" + getString("protocol_TTX_2") + "</option>"); // CMSV6-2  bmlin  230928

	}else if(serverNum == 4){
		//server4协议
		$("#server_protocol_v").append("<option value='0'>" + getString("close") + "</option>");
		if(mycars[1] > 0) $("#server_protocol_v").append("<option value='1'>" + getString("protocol_ttx") + "</option>"); //T标准
		if(mycars[2]> 0)$("#server_protocol_v").append("<option value='2'>" + getString("protocol_rkx") + "</option>");
		if(mycars[3]> 0)$("#server_protocol_v").append("<option value='3'>" + getString("protocol_hw") + "</option>");
		if(mycars[4]> 0)$("#server_protocol_v").append("<option value='4'>" + getString("protocol_808") + "</option>");
		if(mycars[5]> 0)$("#server_protocol_v").append("<option value='5'>" + getString("protocol_transptone") + "</option>");

		if(mycars[6]> 0)$("#server_protocol_v").append("<option value='6'>" + getString("protocol_transpttwo") + "</option>");
		if(mycars[7]> 0)$("#server_protocol_v").append("<option value='7'>" + getString("protocol_8082") + "</option>");
		if(mycars[8]> 0)$("#server_protocol_v").append("<option value='8'>" + getString("protocol_8083") + "</option>");

		if(mycars[9]> 0)$("#server_protocol_v").append("<option value='9'>" + getString("protocol_hwtwo") + "</option>");
		if(mycars[10]> 0)$("#server_protocol_v").append("<option value='10'>" + getString("protocol_transptthree") + "</option>");
		if(mycars[11]> 0)$("#server_protocol_v").append("<option value='11'>" + getString("protocol_28181") + "</option>");

		if(mycars[12]> 0)$("#server_protocol_v").append("<option value='12'>" + getString("protocol_transptfour") + "</option>");
		if(mycars[13]> 0)$("#server_protocol_v").append("<option value='13'>" + getString("protocol_m1") + "</option>");
		if(mycars[14]> 0)$("#server_protocol_v").append("<option value='14'>" + getString("protocol_m2") + "</option>");

		if(mycars[15]> 0)$("#server_protocol_v").append("<option value='15'>" + getString("protocol_m3") + "</option>");
		if(mycars[16]> 0)$("#server_protocol_v").append("<option value='16'>" + getString("protocol_m4") + "</option>");
		if(mycars[17]> 0)$("#server_protocol_v").append("<option value='17'>" + getString("protocol_s1") + "</option>");
		if(mycars[18]> 0)$("#server_protocol_v").append("<option value='18'>" + getString("protocol_s2") + "</option>");
		if(mycars[19]> 0)$("#server_protocol_v").append("<option value='19'>" + getString("protocol_s3") + "</option>");
		if(mycars[31]> 0)$("#server_protocol_v").append("<option value='31'>" + getString("protocol_905_2014") + "</option>");
		if(mycars[37]> 0)$("#server_protocol_v").append("<option value='37'>" + getString("protocol_TIANJIN_808") + "</option>");	// ���� ���808  bmlin  230815
		if(mycars[36]> 0)$("#server_protocol_v").append("<option value='36'>" + getString("protocol_TTX_2") + "</option>"); // CMSV6-2  bmlin  230928

	}else if(serverNum > 4 && serverNum <= 8){
		$("#server_protocol_v").append("<option value='0'>" + getString("close") + "</option>");
		if(mycars[0] > 0) $("#server_protocol_v").append("<option value='1'>" + getString("protocol_808" + serverNum) + "</option>"); //T标准
		if(mycars[1]> 0)$("#server_protocol_v").append("<option value='2'>" + getString("protocol_m" + serverNum) + "</option>");
		if(mycars[2]> 0)$("#server_protocol_v").append("<option value='3'>" + getString("protocol_s" + serverNum) + "</option>");
	}

	磁盘信息
	function typeToName(i, type){
	switch(type){
		case 0:
			setViewText("s_disk_name_v_" + i, "s_disk_name_unknown");
			break;
		case 1:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sata1");
			break;
		case 2:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sata2");
			break;
		case 3:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sata3");
			break;
		case 4:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sata4");
			break;
		case 5:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sd");
			break;
		case 6:
			setViewText("s_disk_name_v_" + i, "s_disk_name_hdd");
			break;
		case 7:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sd3");
			break;
		case 8:
			setViewText("s_disk_name_v_" + i, "s_disk_name_sd4");
			break;
		case 9:
			setViewText("s_disk_name_v_" + i, "s_disk_name_usb1");
			break;
		case 10:
			setViewText("s_disk_name_v_" + i, "s_disk_name_usb2");
			break;
		default:
			break;
	}
}

typeToName(i, data.data.disk[i-1].type);

this.s_disk_name_t = "磁盘名称";
	this.s_disk_total_t = "总容量";
	this.s_disk_free_space_t = "剩余空间";
	this.s_disk_status_t = "状态";
	this.s_disk_name_unknown = "UNKNOWN";
	this.s_disk_name_sata1 = "SATA1";
	this.s_disk_name_sata2 = "SATA2";
	this.s_disk_name_sata3 = "SATA3";
	this.s_disk_name_sata4 = "SATA4";
	this.s_disk_name_sd = "SD1";
	this.s_disk_name_hdd = "SD2/HDD";
	this.s_disk_name_sd3 = "SD3";
	this.s_disk_name_sd4 = "SD4";
	this.s_disk_name_usb1 = "USB";
	this.s_disk_name_usb2 = "USB2";



	#卫星 页面解析数据最多24个
	function updateStatus(data) {
	var number;//gp+bd总个数
	var gp_num = data.data.gp_num;
	var bd_num = data.data.bd_num;
	if((gp_num + bd_num) < 24)
		number = gp_num + bd_num;
	else
		number = 24;
	var tmpgp_num = 0;//实际显示个数
	var tmpbd_num = 0;
	var hideMoodData = data.data.hideMoodData;	// 新增 外置选中 外置GPS，G501 等 时需要隐藏 卫星数据   bmlin   230726

	//是否显示页面
	if(number == 0 || hideMoodData == 1)
	{
		$("#all_moon_info").hide();
		$("#gp_total_count").hide();
	}
	else
	{
		$("#all_moon_info").show();
		$("#gp_total_count").show();
	}

	//具体的卫星的信息
	for (var i = 1; i <= number; i++)
	{
		if(i <= gp_num)
		{
			if(i < 10)
				setViewValue("a_moon_" + i + "_number_v", "(0" + i +"). " + "GP." + data.data.gp_idValue[i-1]);//注意下标
			else
				setViewValue("a_moon_" + i + "_number_v", "(" + i +"). " + "GP." + data.data.gp_idValue[i-1]);//注意下标
			setViewValue("a_moon_" + i + "_signal_v", data.data.gp_dbValue[i-1]);
			tmpgp_num++;
		}
		else
		{
			if(i < 10)
				setViewValue("a_moon_" + i + "_number_v", "(0" + i +"). " + "BD." + data.data.bd_idValue[i-1-gp_num]);
			else
				setViewValue("a_moon_" + i + "_number_v", "(" + i +"). " + "BD." + data.data.bd_idValue[i-1-gp_num]);
			setViewValue("a_moon_" + i + "_signal_v", data.data.bd_dbValue[i-1-gp_num]);
			tmpbd_num++;
		}
		$("#a_moon_" + i + "_info").show();//注意要显示出被隐藏的项
	}
	//多余的卫星项要隐藏
	for (var i = number+1; i <= 24; i++)
	{
		$("#a_moon_" + i + "_info").hide();
	}

	//卫星总颗数显示
	if(tmpgp_num == 0)
	{
		$("#a_gp_total_count").hide();
	}
	else
	{
		setViewValue("a_gp_total_count_v",tmpgp_num);
		$("#a_gp_total_count").show();
	}
	if(tmpbd_num == 0)
	{
		$("#a_bd_total_count").hide();
	}
	else
	{
		setViewValue("a_bd_total_count_v",tmpbd_num);
		$("#a_bd_total_count").show();
	}

	系统页面加速度解析
		// ��ȡ��һ������    bmlin   230331
	var gsensorTmp1 = "(" + "X:" + parseFloat(data.data.g_sensor1.x1/data.data.g_sensor1.unit1).toFixed(2) + ",Y:" + parseFloat(data.data.g_sensor1.y1/data.data.g_sensor1.unit1).toFixed(2)  + ",Z:" + parseFloat(data.data.g_sensor1.z1/data.data.g_sensor1.unit1).toFixed(2) + ")";
	//var gsensorTmp1 = "(" + "X:" + parseFloat(data.data.g_sensor1.x1).toFixed(2) + ",Y:" + parseFloat(data.data.g_sensor1.y1).toFixed(2)  + ",Z:" + parseFloat(data.data.g_sensor1.z1).toFixed(2) + ")";
	// ��ȡ �ڶ�������   bmlin   230331
	var gsensorTmp2 = "(" + "X:" + parseFloat(data.data.g_sensor.x/data.data.g_sensor.unit).toFixed(2) + ",Y:"  + parseFloat(data.data.g_sensor.y/data.data.g_sensor.unit).toFixed(2) + ",Z:"  + parseFloat(data.data.g_sensor.z/data.data.g_sensor.unit).toFixed(2) + " A=" + data.data.g_sensor.a + "°" + ")";
	var tmp_OffSetScalez;
	// ��ȡ�м���ʾ�ķ���   bmlin   230331
	if(data.data.Offsetscalez == 1)
	{
		tmp_OffSetScalez = "-->";
	}
	else
	{
		tmp_OffSetScalez = "->";
	}
	// ���ٶ� ��ʾ����: ��0.00��0.00��1.00��->��0.00��0.00��1.00��16.1��
	//                    ��һ������x,y,z      �ڶ�������x,y,z     �Ƕ�
	setViewValue("s_gsensor_status1_v", gsensorTmp1);	// ��ʾ ���ٶȵ�һ������    bmlin   230331
	setViewValue("s_gsensor_symbol_v", tmp_OffSetScalez);// s_gsensor_symbol ��ʾ �м����  bmlin   230331
	setViewValue("s_gsensor_status2_v", gsensorTmp2);	// ��ʾ ���ٶȵڶ�������    bmlin   230331
	//setViewValue("s_gsensor_status_v", data.data.g_sensor + "°");
}

系统时钟
arr_string = ["GMT-12","GMT-11","GMT-10","GMT-9","GMT-8","GMT-7","GMT-6","GMT-5","GMT-4","GMT-3","GMT-2","GMT-1",
			"GMT+0","GMT+1","GMT+2","GMT+3","GMT+4","GMT+5","GMT+6","GMT+7","GMT+8","GMT+9","GMT+10","GMT+11","GMT+12"];

//时区
	if(defaultConfigData || configData.data.data.system_manage_list.system_clock_list.timezone == 1)
	{
		$("#div_timezone").show();
		if(!defaultConfigData)
		{
			tempdata = configData.data.data.system_manage_list.system_clock_list.timezone_list;
			arr_value = [tempdata.minus_12,tempdata.minus_11,tempdata.minus_10,tempdata.minus_9,tempdata.minus_8,tempdata.minus_7,
				tempdata.minus_6,tempdata.minus_5,tempdata.minus_4,tempdata.minus_3,tempdata.minus_2,tempdata.minus_1,tempdata.plus_0,
				tempdata.plus_1,tempdata.plus_2,tempdata.plus_3,tempdata.plus_4,tempdata.plus_5,tempdata.plus_6,tempdata.plus_7,
				tempdata.plus_8,tempdata.plus_9,tempdata.plus_10,tempdata.plus_11,tempdata.plus_12];
		}
		else
		{
			arr_value = [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1];
		}
		arr_string = ["GMT-12","GMT-11","GMT-10","GMT-9","GMT-8","GMT-7","GMT-6","GMT-5","GMT-4","GMT-3","GMT-2","GMT-1",
			"GMT+0","GMT+1","GMT+2","GMT+3","GMT+4","GMT+5","GMT+6","GMT+7","GMT+8","GMT+9","GMT+10","GMT+11","GMT+12"];
		for(index = 0; index < arr_value.length; index++)
		{
			if(arr_value[index] == 1)
			{
				$("#s_timezone_v").append("<option value=" + index + ">" + arr_string[index] + "</option>");
			}
		}
		$("#s_timezone_v").val(-1);//防止刷新页面时，选项闪过默认值
	}