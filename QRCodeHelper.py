import os
import sys
import logging
from PIL import Image, ImageEnhance, ImageFilter
# import zxing  # 移除zxing库
import json
import subprocess
from pyzbar.pyzbar import decode as pyzbar_decode
import traceback

class QRCodeDecoder:
    """二维码解码辅助类，支持多种解码库"""
    
    def __init__(self, logger=None):
        """初始化解码器
        
        Args:
            logger: 日志记录器，如果为None则创建新的
        """
        self.logger = logger if logger else logging.getLogger("QRCodeDecoder")
        # 移除zxing相关代码
    
    # 移除_fix_zxing_java_path方法
    
    # 移除_patch_zxing_java_execution方法
    
    def decode_qr_code(self, image_path):
        """解码图片中的二维码
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            成功返回二维码内容字符串，失败返回None
        """
        if not os.path.exists(image_path):
            self.logger.error(f"图片文件不存在: {image_path}")
            return None
        
        # 使用增强版解码方法
        try:
            result = self.enhanced_qr_decode(image_path)
            if result:
                # 处理JSON字符串中的转义字符
                result = self.clean_json_string(result)
                return result
        except Exception as e:
            self.logger.error(f"增强解码失败: {str(e)}")
        
        return None
    
    def clean_json_string(self, json_str):
        """清理JSON字符串，处理转义字符问题
        
        Args:
            json_str: JSON字符串
            
        Returns:
            清理后的字符串
        """
        try:
            # 检查是否是JSON格式
            if json_str.startswith('{') and json_str.endswith('}'):
                try:
                    # 尝试解析JSON
                    json_obj = json.loads(json_str)
                    # 再次序列化，确保转义字符正确处理
                    return json.dumps(json_obj, ensure_ascii=False)
                except json.JSONDecodeError:
                    # 不是有效的JSON，返回原始字符串
                    pass
            return json_str
        except Exception as e:
            self.logger.warning(f"清理JSON字符串失败: {str(e)}")
            return json_str
    
    def enhanced_qr_decode(self, image_path):
        """增强版二维码解码，使用图像预处理和多种解码方法
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            成功返回二维码内容字符串，失败返回None
        """
        try:
            # 参考decode_qr.py中的实现
            if not os.path.exists(image_path):
                self.logger.error(f"图片文件不存在: {image_path}")
                return None
            
            self.logger.info(f"开始解码图片: {image_path}")
            
            # 打开图像
            image = Image.open(image_path)
            self.logger.debug(f"图片尺寸: {image.size}, 格式: {image.format}, 模式: {image.mode}")
            
            # 尝试直接解码
            decoded_objects = pyzbar_decode(image)
            if decoded_objects:
                for obj in decoded_objects:
                    data = obj.data.decode('utf-8')
                    self.logger.info(f"直接解码成功: {data}")
                    return data
            
            # 尝试图像预处理，只保留调整大小的方法
            processed_images = [
                # ("灰度图", image.convert('L')),
                # ("增强对比度", ImageEnhance.Contrast(image.convert('L')).enhance(2.0)),
                # ("增加亮度", ImageEnhance.Brightness(image).enhance(1.5)),
                # ("对比度+锐化", ImageEnhance.Contrast(image.convert('L')).enhance(2.0).filter(ImageFilter.SHARPEN)),
                # ("锐化", image.filter(ImageFilter.SHARPEN)),
                ("调整大小2x", image.resize((image.width*2, image.height*2), Image.LANCZOS))
            ]
            
            for i, (desc, img) in enumerate(processed_images):
                self.logger.debug(f"尝试预处理图像: {desc}")
                decoded_objects = pyzbar_decode(img)
                if decoded_objects:
                    for obj in decoded_objects:
                        data = obj.data.decode('utf-8')
                        self.logger.info(f"预处理图像 ({desc}) 解码成功: {data}")
                        return data
            
            self.logger.warning("所有解码方法均失败")
            return None
        except Exception as e:
            self.logger.error(f"增强解码过程中出错: {str(e)}")
            return None

# 提供简单的函数接口
def decode_qr_image(image_path, logger=None):
    """解码图片中的二维码，简单接口
    
    Args:
        image_path: 图片文件路径
        logger: 可选的日志记录器
        
    Returns:
        成功返回二维码内容字符串，失败返回None
    """
    decoder = QRCodeDecoder(logger)
    return decoder.decode_qr_code(image_path)

def pretty_print_json(json_str):
    """美化打印JSON字符串"""
    try:
        if json_str and json_str.startswith('{') and json_str.endswith('}'):
            data = json.loads(json_str)
            return json.dumps(data, ensure_ascii=False, indent=2)
        return json_str
    except:
        return json_str

if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG)
    # 测试识别二维码
    result = decode_qr_image("QrImages/240075264437.jpg")
    print(f"解码结果: {result}")
    
    # 测试JSON转义处理
    test_json = '{"ssid": "240079640426","pwd": "88888888","imei": "866240079640426"}'
    decoder = QRCodeDecoder()
    clean_json = decoder.clean_json_string(test_json)
    print(f"清理后的JSON: {clean_json}")
    
    # 移除zxing相关代码
