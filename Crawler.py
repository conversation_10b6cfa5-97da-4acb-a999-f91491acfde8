import time
import datetime
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# base_url = "http://**************/"
import logging
from INIConfigManager import INIConfigManager
from ConstantTool import Protocol_Map, TimeZone_String, Serial_Info, Disk_Info
import math
import os, functools, json
from Result import Result
# 导入二维码解码模块
from QRCodeHelper import decode_qr_image

try:
    from QRCodeHelper import QRCodeDecoder
except ImportError:
    QRCodeDecoder = None

# 定义统一处理结果
def handle_exceptions(func):
    """
    装饰器：统一捕获异常并返回Result对象
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            return result
        except requests.exceptions.Timeout as e:
            logging.error(f"网络连接超时：{e.args[0]}")
            raise ValueError("网络连接失败")
            # return Result.error(msg="请求超时")
        except requests.exceptions.RequestException as e:
            logging.error(f"网络请求错误：{e.args[0]}")
            raise ValueError("网络连接失败")
            # return Result.error(1, f"网络请求失败: {e.args[0]}")
        except json.JSONDecodeError as e:
            raise
            # return Result.error(1, f"JSON解析失败: {e.args[0]}")
        except KeyError as e:
            raise
            # logging.error("")
            # return Result.error(1, f"字段错误: {e.args[0]}")
        except ValueError as e:
            logging.error(f"值错误：{e.args[0]}")
            raise
            # return Result.error(1, e.args[0])
        except Exception as e:
            logging.error(f"内部错误：{e.args[0]}")
            raise
            # return Result.error(1, f"未知错误: {e.args[0]}")

    return wrapper


class AutoLoginSession:
    def __init__(self, login_url,
                 login_data={"password": "111111"}, timeout=2, max_retries=1, headers={
                "User-Agent": "Mozilla/5.0...",
                "Content-Type": "application/x-www-form-urlencoded"
            }):
        self.login_url = login_url
        self.login_data = login_data
        self.header = headers
        self.timeout = timeout
        self.session = requests.Session()
        self._configure_session(max_retries)
        # 添加会话状态跟踪
        self.last_activity = time.time()
        self.session_valid = False
        self._login()  # 初始登录

    def _configure_session(self, max_retries):
        """配置会话参数和重试策略"""
        retries = Retry(
            total=max_retries,
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504]
        )
        self.session.mount('http://', HTTPAdapter(max_retries=retries))
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    @handle_exceptions
    def _login(self):
        """执行登录并保存会话
        {'data': {'identification': 'admin', 'jsession': '::webs.session::3f7ce688b77ea70cc46f2352af5b8b99',
                  'showAutoTest': 'noshowAutoTest'}, 'result': 0}
        """
        try:
            response = self.session.post(
                self.login_url,
                json=self.login_data,
                timeout=self.timeout
            )
            # 成功才给比如密码什么的都正确了
            data = response.json()
            # 成功恢复
            result = data.get("result", None)
            # 成功
            if result == 0:
                response.raise_for_status()
                # print("登录成功，会话已更新")
                logging.info("登录成功，会话已更新")
                # 更新会话状态
                self.session_valid = True
                self.last_activity = time.time()
            else:
                # result 5 密码错误
                tip = data.get("tip", "password is error")
                logging.error("登录失败:{}".format(tip))
                # 更新会话状态
                self.session_valid = False
                # return {"error": tip}
                raise ValueError(f"{tip}")
                # raise Exception(f"{tip}")
            # else:
            #     raise
        except requests.exceptions.RequestException as e:
            logging.error(f"登录失败: {str(e)}")
            self.session_valid = False
            raise
            # raise Exception(f"登录失败: {str(e)}") from e

    def _is_session_expired(self, response):
        """检测会话是否过期（根据业务特征定制）"""
        # 检查HTTP状态码和响应内容
        if response.status_code == 401 or "登录超时" in response.text:
            return True
            
        # 检查JSON响应中的特定错误码或消息
        try:
            data = response.json()
            # 如果返回的result不是0，可能表示会话已过期
            if data.get("result") not in [0, None]:
                error_msg = data.get("tip", "")
                if "登录" in error_msg or "session" in error_msg.lower() or "超时" in error_msg:
                    return True
        except:
            pass
            
        # 检查会话活动时间，如果超过30分钟没有活动，认为会话可能已过期
        session_timeout = 30 * 60  # 30分钟
        if time.time() - self.last_activity > session_timeout:
            logging.info("会话可能已过期（超过30分钟无活动）")
            return True
            
        return False

    def request_with_retry(self, method, url, **kwargs):
        """带会话检测的请求方法"""
        # 设置默认超时
        kwargs.setdefault('timeout', self.timeout)

        try:
            # 如果会话已标记为无效，先尝试重新登录
            if not self.session_valid:
                logging.info("会话已标记为无效，尝试重新登录...")
                self._login()
                
            # 发送请求
            response = self.session.request(method, url, **kwargs)
            
            # 更新最后活动时间
            self.last_activity = time.time()
            
            # 检查会话是否过期
            if self._is_session_expired(response):
                logging.info("检测到会话过期，尝试重新登录...")
                self._login()
                response = self.session.request(method, url, **kwargs)  # 重试请求
                self.last_activity = time.time()  # 再次更新活动时间
                
            return response
        except requests.exceptions.ConnectionError as e:
            logging.error(f"连接异常，尝试重新登录: {str(e)}")
            self.session_valid = False
            self._login()  # 尝试重新登录
            # 重试请求
            return self.session.request(method, url, **kwargs)

    # 封装常用方法
    def get(self, url, **kwargs):
        return self.request_with_retry('GET', url, **kwargs)

    def post(self, url, data=None, json=None, **kwargs):
        return self.request_with_retry('POST', url, data=data, json=json, **kwargs)

    def put(self, url, data=None, **kwargs):
        return self.request_with_retry('PUT', url, data=data, **kwargs)

    def get_session_status(self):
        """获取会话状态信息"""
        return {
            "valid": self.session_valid,
            "last_activity": datetime.datetime.fromtimestamp(self.last_activity).strftime('%Y-%m-%d %H:%M:%S'),
            "idle_time": f"{(time.time() - self.last_activity) / 60:.1f}分钟"
        }


class CrawlerFunc:
    def __init__(self, logger, iniconfig):
        self.base_url = iniconfig.get("CrawlerConfing", "BaseUrl", "http://**************")
        self.logger = logger
        self.iniconfig = iniconfig
        try:
            self.client = self.get_login_info()
        except Exception as e:
            raise

        # 获取参数

    # 统一处理非Str为str
    def union_str_return(self, data):
        """等处理空值等等"""
        if isinstance(data, str):
            return data
        elif isinstance(data, int):
            return str(data)
        elif isinstance(data, float):
            return "{0:.2f}".format(data)
        elif isinstance(data, list):
            return ",".join(data)
        else:
            return data

    def get_login_info(self):
        # 纯数子为解析为数值iNi纯数值，不自动转换
        password = self.iniconfig.get("CrawlerConfing", "PassWord", "111111", False)
        timeout = self.iniconfig.get("CrawlerConfing", "TimeOut", 2)
        max_retries = self.iniconfig.get("CrawlerConfing", "max_retries", 1)
        login_url = f"{self.base_url}/action/content?type=login&func=putlogin"
        login_data = {"password": password}
        return AutoLoginSession(login_url=login_url, login_data=login_data, max_retries=max_retries, timeout=timeout)

    @handle_exceptions
    def get_sys_info(self, url="/action/content?type=info&func=system", command=None, configVariant={}):
        """"
        系统信息页面
        key 获取具体哪个项目，没有就返回全部
        AiVersion：扩展版本
        ChipID:
        appVer：APP版本
        sysPower：系统电源
        acc: 1:on 0 off
        if (parseInt(data.data.acc, 10) == 0) {
    setViewValue('s_acc_status_v', 'OFF');
  } else if (parseInt(data.data.acc, 10) == 1) {
    setViewValue('s_acc_status_v', 'ON');
  }
        Imei:
        Imsi:
        Model_4G:4G模块（版本号）
        mcuVer:Mcu版本
        ExtVer： Ai认证: 正常/不正常 1正常/0

            if (parseInt(data.data.ExtVer, 10) == 0)
        {
            setViewHtml("s_ExVer_v", "disk_abnormal");
        }
        else
        {
            setViewHtml("s_ExVer_v", "disk_normal");
        }
        {
        g_sensor：加速度 g_sensor1,g_sensor,Offsetscalez

        js:gsensor_status2 对应 g_sensor 带角度
        s_gsensor_status1 对应g_sensor 不带角度
        Offsetscalez  ==1:--> 其他-> s_gsensor_symbol_v

        即   <span id="s_gsensor_status1_v">(X:0.05,Y:0.14,Z:1.02)</span>
            <span id="s_gsensor_symbol_v">-->;</span>
            <span id="s_gsensor_status2_v">(X:0.00,Y:0.00,Z:0.00 A=0.0°)</span>
        #返回结果
       "data" : {
          "AiVersion" : "C-V0,AIPCM-V25011300",
          "ChipID" : "",
          "ExtVer" : 1,
          "HideDisk_temperature" : 1,
          "Imei" : "866240075264437",
          "Imsi" : "",
          "MainRecChnNum" : 2,
          "MainRecState" : 0,
          "Model_4G" : "",
          "ODMCfgVersion" : "",
          "OdmType" : "0.0",
          "Offsetscalez" : 1,
          "acc" : 1,
          "adInformation" : 0,
          "appVer" : "148-M02A-A25010302.28970-C",
          "cupTemperature" : "66.19",
          "devID" : "240075264437",
          "fatigue" : 0,
          "g_sensor" : {
             "a" : "0.0",
             "unit" : 100,
             "x" : 0,
             "y" : 0,
             "z" : 0
          },
          "g_sensor1" : {
             "unit1" : 4096,
             "x1" : 219,
             "y1" : 598,
             "z1" : 4029
          },
          "gps" : {
             "altitude" : 0,
             "isGpsExist" : 0,
             "latitudeCent" : 0,
             "latitudeDegree" : 0,
             "latitudeSec" : 0,
             "longitudeCent" : 0,
             "longitudeDegree" : 0,
             "longitudeSec" : 0,
             "positionMode" : 0,
             "satelliteCount" : 0,
             "speed" : 0,
             "status" : 0
          },
          "hardDiskTenp" : "[0:0]5.00",
          "hide_lock_status" : 1,
          "iOStatus" : 1,
          "isSa206" : 1,
          "is_T2001" : 0,
          "lock" : 1,
          "mcuVer" : "",
          "plateNo" : "",
          "showNum" : 1,
          "sn" : "014816398318",
          "sysPower" : "12.0V"
       },
       "result" : 0
    }


        """
        # 后续接口调用（无需手动处理会话）
        # 获取用户信息
        try:
            sys_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"系统设置页面data:{json.dumps(sys_info, ensure_ascii=False)}")
            if sys_info.get("result") == 0 and sys_info.get("data", None) is not None:
                # 成功
                data = sys_info.get("data")
                if command and data.get(command, None) is not None:
                    # 加速度
                    if command == "g_sensor":
                        g_sensor = data.get("g_sensor")
                        g_sensor1 = data.get("g_sensor1")
                        offsetscalez = data.get("Offsetscalez")
                        if g_sensor and g_sensor1 and offsetscalez:
                            g1_x = "{0:.2f}".format(round(g_sensor1.get("x1") / g_sensor1.get("unit1"), 2))
                            g1_y = "{0:.2f}".format(round(g_sensor1.get("y1") / g_sensor1.get("unit1"), 2))
                            g1_z = "{0:.2f}".format(round(g_sensor1.get("z1") / g_sensor1.get("unit1"), 2))

                            g_x = "{0:.2f}".format(round(g_sensor.get("x") / g_sensor.get("unit"), 2))
                            g_y = "{0:.2f}".format(round(g_sensor.get("y") / g_sensor.get("unit"), 2))
                            g_z = "{0:.2f}".format(round(g_sensor.get("z") / g_sensor.get("unit"), 2))
                            g_a = g_sensor.get("a") + "°"
                            offset = "1" if offsetscalez == 1 else "0"
                            data = g1_x + "," + g1_y + "," + g1_z + "," + g_x + "," + g_y + "," + g_z + "," + g_a + "," + offset
                            # data = g_x + "," + g_y + "," + g_z + ","+ g_a +","+g1_x + "," + g1_y + "," + g1_z + "," + offset
                            # return Result.success(value={command: data}, configVariant=configVariant)
                    # acc
                    elif command == "acc":
                        data = "ON" if data.get(command) else "OFF"
                    # ExtVer Ai认证
                    elif command == "ExtVer":
                        data = "不正常" if data == 0 else "正常"
                    else:
                        data = data.get(command)
                    data = self.union_str_return(data)
                    return Result.success(value={command: data}, configVariant=configVariant)
                else:
                    self.logger.error("解析系统该信息页面错误")
                    raise ValueError("解析系统信息页面错误")
            else:
                raise ValueError("解析系统信息页面错误")
        except Exception as e:
            raise

    # wifi设置
    @handle_exceptions
    def get_wifi_setting_info(self, url="/action/content?type=wifi&func=get", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:

        ssid: SSID
        pwd:密码
        authMode: 认证模式：1：共享模式，2：WPA,3:WPA-PSK
        open_t:开放模式 share_t共享模式
        if(data.data.hideModeOpen_WIFI == 1)
		{
			$("#auth_mode_v").append("<option value='1'>" + getString("share_t") + "</option>");
			$("#auth_mode_v").append("<option value='2'>" + 'WPA' + "</option>");
			$("#auth_mode_v").append("<option value='3'>" + 'WPA-PSK' + "</option>");
		}
		else
		{
			$("#auth_mode_v").append("<option value='0'>" + getString("open_t") + "</option>");
			$("#auth_mode_v").append("<option value='1'>" + getString("share_t") + "</option>");
			$("#auth_mode_v").append("<option value='2'>" + 'WPA' + "</option>");
			$("#auth_mode_v").append("<option value='3'>" + 'WPA-PSK' + "</option>");
		}
        encrypt: //加密类型 0-NONE 1-TKIP 2AES
        $("#encrypt_type_v").append("<option value='0'>" + 'NONE' + "</option>");
        $("#encrypt_type_v").append("<option value='1'>" + 'TKIP' + "</option>");
        $("#encrypt_type_v").append("<option value='2'>" + 'AES' + "</option>");
        encType: 加密类型实际拿的是encType 0-NONE 1-WEP 2-TKIP 3-AES
        如下
        var tmp_encrype = data.data.encType;
            if(data.data.encType < 0)
                tmp_encrype = 0;
            else if(data.data.encType > 2)
                tmp_encrype = 2;
            $("#encrypt_type_v").val(tmp_encrype);

        workMode:wifi用途
        //工作模式 0-station 1-ap
        $("#work_mode_v").append("<option value='0'>" + 'Station' + "</option>");
        $("#work_mode_v").append("<option value='1'>" + 'AP' + "</option>");
{'data': {'authMode': 3, 'enable': 1, 'encType': 2, 'encrypt': 1, 'gateWay': '0.0.0.0', 'hideModeOpen_WIFI': 1, 'ipAddr': '***************', 'isDhcp': 1, 'is_sa223': 1, 'onlyApMode': 1, 'pwd': '88888888', 'ssid': '240075261987', 'subNet': '***************', 'version': '*******', 'workMode': 1}, 'result': 0}
        """
        # 获取wifi设置信息
        try:
            wifi_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"wifi设置页面data:{json.dumps(wifi_info)}")
            if wifi_info.get("result") == 0 and wifi_info.get("data", None) is not None:
                # 成功
                data = wifi_info.get("data")
                if command and data.get(command, None) is not None:
                    data = data.get(command)
                    # 认证模式
                    if command == "authMode":
                        if data == 1:
                            data = "共享模式"
                        elif data == 2:
                            data = "WPA"
                        elif data == 3:
                            data = "WPA-PSK"
                        else:
                            data = "开放模式"
                    # 用途
                    elif command == "workMode":
                        data = "AP" if data else "Station"
                    # 加密类型
                    elif command == "encType":
                        if data > 2:
                            data = 2
                        if data < 0:
                            data = 0
                        if data == 1:
                            data = "TKIP"
                        elif data == 2:
                            data = "AES"
                        else:
                            data = "None"
                    data = self.union_str_return(data)
                    return Result.success(value={command: data}, configVariant=configVariant)
                else:
                    self.logger.info(f"解析wifi设置错误指令错误")
                    raise ValueError("解析wifi设置错误指令错误")
            else:
                raise ValueError("解析wifi设置错误")
        except Exception as e:
            raise

    # 终端配置设置
    @handle_exceptions
    def get_terminal_setting_info(self, url="/action/content?type=regist&func=get", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:

        devID: 设备号

        """
        # 获取终端设置设置信息
        try:
            terminal_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"终端设置信息 data{json.dumps(terminal_info)}")
            if terminal_info.get("result") == 0 and terminal_info.get("data", None) is not None:
                # 成功
                data = terminal_info.get("data")
                if data and command and data.get(command, None) is not None:
                    data = data.get(command)
                    data = self.union_str_return(data)
                    return Result.success(value={command: data})
                else:
                    self.logger.error(f"解析终端配置信息command={command}失败")
                    raise ValueError("解析终端配置信息command失败")
            else:
                raise ValueError("解析终端配置信息data失败")
        except Exception as e:
            raise

    # 中心设置 截图是点击进去的,是只要测中心服务器1就可以了？详见参考center_protocol.txt，测试中心1-4的都是这个列表遍历的内容
    @handle_exceptions
    def get_center_setting_info(self, url="/action/content?type=center&func=get", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:
        服务器1-几 按循环跑，正常是不是取样中心1 即函数列表第一个：？？？

        protocolType : 4:部标2013_M1   0；关闭，还有很多，其他的不判断的了，有算法，ConstantTool-->Protocol_Map 字典
        port:端口
        ip:ip地址
        gpsInterval：GPS间隔



        {
           "data" : {
              "botton28181Set" : 1,
              "bottonMqttSet" : 0,
              "max_PROTOCOL" : 39,
              "server" : [
                 {
                    "agreement" : "1100100000000100000000000000000000000000000000000000000000000000",
                    "bakip" : "*************",
                    "baktcpport" : 9000,
                    "bakudpport" : 9000,
                    "conntype" : 0,
                    "differentdevice" : 9000,
                    "enable" : 1,
                    "gpsInterval" : 20,
                    "ip" : "************",
                    "lock_ip_enable" : 0,
                    "mainudpport" : 8000,
                    "port" : 5022,
                    "protocolType" : 4
                 },
                 {
                    "agreement" : "1000000100000010000000000000000100000000000000000000000000000000",
                    "bakip" : "*************",
                    "baktcpport" : 9000,
                    "bakudpport" : 9000,
                    "conntype" : 0,
                    "differentdevice" : 9000,
                    "enable" : 1,
                    "gpsInterval" : 20,
                    "ip" : "*************",
                    "lock_ip_enable" : 0,
                    "mainudpport" : 8000,
                    "port" : 6608,
                    "protocolType" : 0
                 },
                 {
                    "agreement" : "1000010010010001000000000000000000000000000000000000000000000000",
                    "bakip" : "*************",
                    "baktcpport" : 9000,
                    "bakudpport" : 9000,
                    "conntype" : 0,
                    "differentdevice" : 9000,
                    "enable" : 1,
                    "gpsInterval" : 20,
                    "ip" : "*************",
                    "lock_ip_enable" : 0,
                    "mainudpport" : 8000,
                    "port" : 6608,
                    "protocolType" : 0
                 },
                 {
                    "agreement" : "1000001000000000110000000000010100000000000000000000000000000000",
                    "bakip" : "*************",
                    "baktcpport" : 9000,
                    "bakudpport" : 9000,
                    "conntype" : 0,
                    "differentdevice" : 9000,
                    "enable" : 1,
                    "gpsInterval" : 20,
                    "ip" : "*************",
                    "lock_ip_enable" : 0,
                    "mainudpport" : 8000,
                    "port" : 6608,
                    "protocolType" : 0
                 },
                 {
                    "agreement" : "0000000000000000000000000000000000000000000000000000000000000000",
                    "bakip" : "",
                    "baktcpport" : 0,
                    "bakudpport" : 0,
                    "conntype" : 0,
                    "differentdevice" : 0,
                    "enable" : 0,
                    "gpsInterval" : 20,
                    "ip" : "",
                    "mainudpport" : 0,
                    "port" : 0,
                    "protocolType" : 0
                 },
                 {
                    "agreement" : "0000000000000000000000000000000000000000000000000000000000000000",
                    "bakip" : "",
                    "baktcpport" : 0,
                    "bakudpport" : 0,
                    "conntype" : 0,
                    "differentdevice" : 0,
                    "enable" : 0,
                    "gpsInterval" : 20,
                    "ip" : "",
                    "mainudpport" : 0,
                    "port" : 0,
                    "protocolType" : 0
                 },
                 {
                    "agreement" : "0000000000000000000000000000000000000000000000000000000000000000",
                    "bakip" : "",
                    "baktcpport" : 0,
                    "bakudpport" : 0,
                    "conntype" : 0,
                    "differentdevice" : 0,
                    "enable" : 0,
                    "gpsInterval" : 20,
                    "ip" : "",
                    "mainudpport" : 0,
                    "port" : 0,
                    "protocolType" : 0
                 },
                 {
                    "agreement" : "0000000000000000000000000000000000000000000000000000000000000000",
                    "bakip" : "",
                    "baktcpport" : 0,
                    "bakudpport" : 0,
                    "conntype" : 0,
                    "differentdevice" : 0,
                    "enable" : 0,
                    "gpsInterval" : 120,
                    "ip" : "tjoin.online",
                    "mainudpport" : 0,
                    "port" : 9808,
                    "protocolType" : 0
                 }
              ],
              "serverNum" : 4,
              "version" : "*******"
           },
           "result" : 0
        }

        """
        # 获取中心设置设置信息
        try:
            center_info = self.client.get(f"{self.base_url}{url}").json()
            # self.logger.info(f"获取中心设置设置信息data:{json.dumps(center_info)}")
            if center_info.get("result") == 0:
                # 成功
                servers = center_info.get("data").get("server")
                serverNum = center_info.get("data").get("serverNum")
                self.logger.info(f"获取中心设置设置信息data:{json.dumps(center_info)}")
                if servers:
                    # 获取第一个server: 没有参数的时候
                    index = 1
                    if configVariant:
                        index = configVariant.get("NUM", "")
                        # if isinstance(index, str) and index.isdigit():
                        #     index = int(index)
                        if not index or not isinstance(index, str) or not index.isdigit():
                            self.logger.error(f"传递的参数有误，index:{index}需要为字符串")
                            return Result.error(f"传递的参数有误")
                        # 判断索引是否超过
                        else:
                            index = int(index)
                        if index < 0 or serverNum < index:
                            self.logger.error(f"参数错误，中心服务器台数{str(serverNum)}索引为{str(index)}超过")
                            return Result.error(f"参数错误，中心服务器台数{str(serverNum)}索引为{str(index)}超过")

                    data = servers[index - 1]
                    if command:
                        data = data.get(command)
                        # 协议
                        if command == "protocolType":
                            data = Protocol_Map.get(data, '')
                        data = self.union_str_return(data)
                        return Result.success(value={command: data})
                else:
                    self.logger.info("解析中心设置中心设置信息")
                    raise ValueError("解析中心设置中心设置信息")
            else:
                raise ValueError("解析中心设置信息失败")
        except Exception as e:
            self.logger.error(f"中心设置信息{e.args[0]}")
            raise

    # 拨号设置
    @handle_exceptions
    def get_dialup_setting_info(self, url="/action/content?type=dialup&func=get", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:

        apn: 接入点
        centerNo:中心号
        user:用户名
        password:密码

        auth_type 鉴权方式 1:PAP 2:CHAP 没需求
        var tmp_autoType = (data.data.auth_type != 2) ? 1 : 2;
		$("#auth_type_v").val(tmp_autoType);

		{
           "data" : {
              "apn" : "ctnet",
              "auth_type" : 0,
              "centerNo" : "*99***1#",
              "enable" : 1,
              "password" : "card",
              "smsService" : "13800138000",
              "type" : 4,
              "user" : "card",
              "version" : "*******"
           },
           "result" : 0
        }

        """
        # 获取拨号设置信息
        try:
            dialup_info = self.client.get(f"{self.base_url}{url}").json()
            logging.info(f"获取拨号数据 data{json.dumps(dialup_info)}")
            if dialup_info.get("result") == 0 and dialup_info.get("data", None) is not None:
                # 成功
                data = dialup_info.get("data")
                if command and data.get(command, None) is not None:
                    data = data.get(command)
                    if command == "auth_type":
                        data = 1 if data != 2 else data
                        data = "CHAP" if data == 2 else "PAP"
                    data = self.union_str_return(data)
                    return Result.success(value={command: data}, configVariant=configVariant)
                else:
                    logging.error(f"解析拨号设置失败:数据或者指令{command}不存在")
                    raise ValueError("解析拨号设置失败")
            else:
                raise ValueError("解析拨号设置失败")
        except Exception as e:
            raise

    # 获取二维码页面
    @handle_exceptions
    def download_qr_code(self, url="/action/content?type=QRcode_info_show&func=get_img_src", command=None,
                         configVariant={}):
        """
        :param url:
        :param qr_name:
        :param command:
        :param configVariant: 可包含qr_code(文件名)和need_decode(是否需要解码，值为字符串"1"时才有效)参数
        :return:
        先通过get_img_src获取图片文件
        在下载文件
        如果need_decode="1"，则会尝试识别二维码内容并返回，同时返回图片路径
        """
        # 校验参数
        try:
            
            qr_name = configVariant.get("qr_code")
            need_decode = configVariant.get("need_decode", False)
            
            # 判断是不是字符串
            if qr_name is None or not isinstance(qr_name, str):
                self.logger.error(f"指令{command}的参数错误")
                return Result.error(msg=f"指令{command}的参数错误")
                
            # 获取图片名称
            response = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"get qr code url data:{json.dumps(response)}")
            ImgSrc = response.get('data').get("ImgSrc", False)
            
            if ImgSrc:
                # 下载图片的地址
                img_url = f"{self.base_url}/{ImgSrc}"
                # 获取图片后缀
                filenane, ext = os.path.splitext(ImgSrc)
                ext = ext if ext else ".jpg"
                # 获取路径
                # ini获取图片路径
                ImageDir = self.iniconfig.get("CrawlerConfing", "ImageDir", None)
                ImageDir = ImageDir if ImageDir else "QrImages"
                # 创建文件目录
                if not os.path.exists(ImageDir):
                    os.makedirs(ImageDir)

                save_path = f"{ImageDir}/{qr_name}{ext}"
                self.logger.info(f"upload qr code path:{save_path}")
                # 获取图片
                try:
                    response = self.client.get(img_url)
                    if response.status_code == 200:
                        with open(save_path, 'wb') as f:
                            f.write(response.content)
                        
                        # 如果需要解码二维码，只有need_decode值为字符串"1"时才有效
                        if need_decode == "1":
                            try:
                                # 解码二维码
                                qr_content = decode_qr_image(save_path, self.logger)
                                
                                if qr_content:
                                    self.logger.info(f"二维码识别成功: {qr_content}")
                                    
                                    # 处理JSON内容 - 如果是JSON字符串，转换为Python对象
                                    try:
                                        if qr_content.strip().startswith('{') and qr_content.strip().endswith('}'):
                                            # 尝试解析为JSON对象
                                            content_obj = json.loads(qr_content)
                                            # 返回图片路径和解码内容
                                            return Result.success(configVariant=configVariant, 
                                                                 value={command: save_path, "qr_decode": content_obj})
                                    except Exception as e:
                                        self.logger.debug(f"JSON解析失败，使用原始字符串: {str(e)}")
                                    
                                    # 如果不是JSON或解析失败，直接返回字符串
                                    return Result.success(configVariant=configVariant, 
                                                         value={command: save_path, "qr_decode": qr_content})
                                else:
                                    self.logger.warning(f"二维码识别失败: {save_path}")
                                    return Result.success(configVariant=configVariant, 
                                                         value={command: save_path, "qr_decode": "识别失败"})
                            except ImportError:
                                self.logger.error("QRCodeHelper模块未找到，无法识别二维码")
                                return Result.success(configVariant=configVariant, 
                                                     value={command: save_path, "qr_decode": "缺少解码模块"})
                            except Exception as e:
                                self.logger.error(f"二维码识别过程中出错: {str(e)}")
                                return Result.success(configVariant=configVariant, 
                                                     value={command: save_path, "qr_decode": f"错误: {str(e)}"})
                        
                        # 原有功能，不解码时直接返回路径
                        return Result.success(configVariant=configVariant, value={command: save_path})
                    else:
                        self.logger.error(f"upload qr code")
                        raise ValueError("获取二维码失败")
                except Exception as e:
                    self.logger.error(f"获取二维码失败{e.args[0]}")
                    raise
            else:
                self.logger.error(f"获取二维码失败,解析图片路径失败ImgSrc")
                raise ValueError("获取二维码失败")
        except Exception as e:
            raise

    # 网络信息
    @handle_exceptions
    def get_net_info(self, url="/action/content?type=info&func=net", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:

        simSignal: SIM卡信号 dial
        simStatus:SIM信息 dial 1:存在 0不存在

        "data" : {
          "Center_Num_Max" : 4,
          "centerNumber" : 0,
          "dial" : {
             "Module_Exist" : 1,
             "dialIp" : "0.0.0.0",
             "dialStatus" : 0,
             "gw" : "0.0.0.0",
             "moduleType" : 0,
             "netType" : 0,
             "simSignal" : 0,
             "simStatus" : 0
          },
          "exist" : 0,
          "ftpstate" : 0,
          "hideNetworkStatus" : 1,
          "is_T2001" : 0,
          "link" : 0,
          "netLinked" : 2,
          "server" : [
             {
                "pAddr" : "************",
                "port" : 5022,
                "status" : 0
             },
             {
                "pAddr" : "*************",
                "port" : 6608,
                "status" : 0
             },
             {
                "pAddr" : "*************",
                "port" : 6608,
                "status" : 0
             },
             {
                "pAddr" : "*************",
                "port" : 6608,
                "status" : 0
             },
             {
                "pAddr" : "",
                "port" : 0,
                "status" : 0
             },
             {
                "pAddr" : "",
                "port" : 0,
                "status" : 0
             },
             {
                "pAddr" : "",
                "port" : 0,
                "status" : 0
             },
             {
                "pAddr" : "tjoin.online",
                "port" : 9808,
                "status" : 0
             }
          ],
          "shownetstate" : 1,
          "wifi" : {
             "gw" : "0.0.0.0",
             "ip" : "**************",
             "module" : 1,
             "signal" : "100",
             "ssid" : "240075264437"
          }
       },
       "result" : 0

        """
        try:
            # 网络信息
            net_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"获取网络信息数据data:{json.dumps(net_info)}")
            if net_info.get("result") == 0 and net_info.get("data", None) is not None:
                # 成功
                data = net_info.get("data")
                # SIM
                # dial_data = data.get("dial")
                if command and data.get("dial", None) and data.get("dial").get(command, None) is not None:
                    data = data.get("dial").get(command)
                    # sim卡
                    if command == "simStatus":
                        data = "存在" if data else "不存在"
                    if not isinstance(data, str):
                        data = str(data)

                    # 把字符串变成str
                    data = self.union_str_return(data)
                    return Result.success(value={command: data}, configVariant=configVariant)
                else:
                    raise ValueError(f"解析网络数据command{command}失败")
            else:
                raise ValueError("解析网络数据失败data失败")
        except Exception as e:
            raise

    # 磁盘信息
    @handle_exceptions
    def get_disk_info(self, url="/action/content?type=info&func=disk", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:
            {
       "command":"status",
       "ConfigVariant":
       {
        "type":5

           }
         }
        检查磁盘为SD1？只获取SD1的？现在默认读取SD1的，

        type: 5=SD1 多少个类型其他匹配看txt文件,更加传递的
        size：总容量
        free：剩余空间
        status：剩余空间 0不存在，1正常，2不正常
        if(parseInt(data.data.disk[i-1].status, 10) == 0){
			setViewValue("s_disk_status_v_" + i, getString("not_exist"));
		}
		else if(parseInt(data.data.disk[i-1].status, 10) == 1){
			setViewValue("s_disk_status_v_" + i, getString("disk_normal"));
		}
		else if(parseInt(data.data.disk[i-1].status, 10) == 2){
			setViewValue("s_disk_status_v_" + i, getString("disk_abnormal"));

        {
       "data" : {
          "disk" : [
             {
                "free" : "0GB",
                "size" : "0GB",
                "status" : 0,
                "type" : 5
             },
             {
                "free" : "0GB",
                "size" : "0GB",
                "status" : 0,
                "type" : 9
             }
          ],
          "diskcount" : 2
       },
       "result" : 0
    }
        """
        try:
            # 获取磁盘设置信息
            # 没有参数就获取sd的
            type = 5
            type_name = "SD1"
            if configVariant:
                type_name = configVariant.get("type", "")
                if not type_name or not isinstance(type_name, str):
                    self.logger.error(f"传递的参数有误，type:{type_name}需要为字符串")
                    return Result.error(f"传递的参数有误")
                else:
                    if type_name in Disk_Info.values():
                        # 获取所有匹配的key（可能多个）
                        types = [k for k, v in Disk_Info.items() if v == type_name]
                        type = types[0] if types else 5

                    # 判断是否在Disk_info中
                    else:
                        self.logger.error(f"传递的参数有误，磁盘名称:{type_name}不存在")
                        return Result.error(f"传递的参数有误，磁盘名称:{type_name}不存在")

            disk_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"获取磁盘信息{json.dumps(disk_info)}")
            if disk_info.get("result") == 0 and disk_info.get("data", None) is not None:
                # 成功
                disk_data = disk_info.get("data").get("disk")
                if disk_data:
                    for disk in disk_data:
                        # sd1
                        if disk.get("type") == type:
                            if command:
                                data = disk.get(command)
                                if command == "status":
                                    if data == 1:
                                        data = "正常"
                                    elif data == 2:
                                        data = "不正常"
                                    else:
                                        data = "不存在"
                                # 磁盘名称
                                elif command == "type":
                                    data = Disk_Info[data]
                                data = self.union_str_return(data)
                                return Result.success(value={command: data}, configVariant=configVariant)
                    # 没找到磁盘
                    self.logger.error(f"没有找到该类型磁盘type={type_name}")
                    return Result.error(msg=f"没有找到该类型磁盘type={type_name}")

                else:
                    raise ValueError("解析磁盘信息错误")
            else:
                raise ValueError("解析磁盘信息错误")
        except Exception as e:
            raise

    # 卫星信息
    @handle_exceptions
    def get_moon_info(self, url="/action/content?type=info&func=moon", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:
        详细解析看center_protocol.txt

        检查磁盘为SD1？只获取SD1的？现在默认读取SD1的，
        gp_num：gps总数
        bd_num：bp总数
        gp_idValue: gps卫星编号 如GP." + data.data.gp_idValue[i-1]) 返回应该是列表
        gp_dbValue：卫星强度 data.data.gp_dbValue[i-1]

            {
           "data" : {
              "bd_dbValue" : null,
              "bd_idValue" : null,
              "bd_num" : 0,
              "gp_dbValue" : null,
              "gp_idValue" : null,
              "gp_num" : 0,
              "hideMoodData" : 2
           },
           "result" : 0
        }
        """
        try:
            # 获取卫星设置信息
            moon_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"获取卫星信息数据data{json.dumps(moon_info)}")
            if moon_info.get("result") == 0 and moon_info.get("data", None) is not None:
                # 成功
                data = moon_info.get("data")
                if command:
                    if command == "gps":
                        data_gps = data.get("gp_dbValue")
                        # 如果没有返回[]
                        data = ""
                        if data_gps is None:
                            data = ""
                        # 遍历并构成字符串
                        else:
                            data = ",".join([str(num) for num in data_gps])
                        return Result.success(value={command: data}, configVariant=configVariant)
                    elif command == "bd":
                        data_bd = data.get("bd_dbValue")
                        data = ""
                        if data_bd is None:
                            data = ""
                        else:
                            data = ",".join([str(num) for num in data_bd])
                        data = self.union_str_return(data)
                        return Result.success(value={command: data}, configVariant=configVariant)
                    else:
                        raise ValueError("解析卫星设置数据失败")
                else:
                    raise ValueError("解析卫星设置数据失败")
            else:
                raise ValueError("解析卫星设置数据失败")
        except Exception as e:
            raise

    # 系统时钟（时区）系统管理 -- 系统时钟
    @handle_exceptions
    def get_clock_info(self, url="/action/content?type=clock&func=get", command=None, configVariant={}):
        """

        :param self:
        :param url:
        :param command:
        :return:

        时区
        data.timezone = (parseInt($("#s_timezone_v").val(), 10))*10 + (($("#s_timezone_two_v").val()/15)*25)/10;
        $("#s_timezone_v").val(Math.floor((data.data.time.timezone*10)/100));
        arr_string = ["GMT-12","GMT-11","GMT-10","GMT-9","GMT-8","GMT-7","GMT-6","GMT-5","GMT-4","GMT-3","GMT-2","GMT-1",
			"GMT+0","GMT+1","GMT+2","GMT+3","GMT+4","GMT+5","GMT+6","GMT+7","GMT+8","GMT+9","GMT+10","GMT+11","GMT+12"];
        {
           "data" : {
              "dst" : {
                 "dstMode" : 0,
                 "info" : {
                    "calValue" : {
                       "Version" : "1.0.1.1",
                       "Year" : 0,
                       "eDate" : 0,
                       "eHour" : 0,
                       "eMonth" : 0,
                       "eWeek" : 0,
                       "e_eday" : 0,
                       "e_sday" : 0,
                       "e_weeks" : 0,
                       "m_niCLock_L0" : 19,
                       "m_niCLock_L1" : 45,
                       "m_niCLock_L2" : 6,
                       "m_niCLock_L3" : 0,
                       "m_niDate_D0" : 2025,
                       "m_niDate_D1" : 3,
                       "m_niDate_D2" : 24,
                       "m_niDate_D3" : 0,
                       "reserve" : "",
                       "sDate" : 0,
                       "sHour" : 0,
                       "sMonth" : 0,
                       "sWeek" : 0,
                       "s_eday" : 0,
                       "s_sday" : 0,
                       "s_weeks" : 0,
                       "savingTime" : 0,
                       "turn_emonth" : 0,
                       "turn_eweek" : 0,
                       "turn_smonth" : 0,
                       "turn_sweek" : 0
                    },
                    "showValue" : {
                       "end_time_hour_end" : 23,
                       "end_time_hour_start" : 0,
                       "end_time_hour_value" : 0,
                       "end_time_month_end" : 11,
                       "end_time_month_start" : 0,
                       "end_time_month_value" : 0,
                       "end_time_week_end" : 6,
                       "end_time_week_start" : 0,
                       "end_time_week_value" : 0,
                       "end_time_weekn_end" : 4,
                       "end_time_weekn_start" : 0,
                       "end_time_weekn_value" : 0,
                       "start_time_hour_end" : 23,
                       "start_time_hour_start" : 0,
                       "start_time_hour_value" : 0,
                       "start_time_month_end" : 11,
                       "start_time_month_start" : 0,
                       "start_time_month_value" : 0,
                       "start_time_week_end" : 6,
                       "start_time_week_start" : 0,
                       "start_time_week_value" : 0,
                       "start_time_weekn_end" : 4,
                       "start_time_weekn_start" : 0,
                       "start_time_weekn_value" : 0
                    }
                 },
                 "offset" : 0
              },
              "time" : {
                 "buzzerSwitch" : 1,
                 "date" : "2025/3/24",
                 "dateType" : 0,
                 "ntpport" : "123",
                 "ntpserver" : "ntp.ubuntu.com",
                 "time" : 71106,
                 "timeOut" : 60,
                 "timezone" : 200.0,
                 "version" : "1.0.0.4"
              }
           },
           "hidePreviewChannel" : 1,
           "result" : 0
        }

        """
        try:
            # 获取时钟设置
            clock_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"系统时钟设置数据data{json.dumps(clock_info)}")
            if clock_info.get("result") == 0 and clock_info.get("data", None) is not None and clock_info.get(
                    "data").get("time", None) is not None:
                # 成功
                data_time = clock_info.get("data").get("time")
                if command and data_time.get("timezone", None) is not None:
                    timezone = data_time.get("timezone")
                    index = math.floor((timezone * 10) / 100)
                    data = TimeZone_String[index]
                    data = self.union_str_return(data)
                    return Result.success(value={command: data}, configVariant=configVariant)
                else:
                    raise ValueError("解析时钟设置错误")

            else:
                raise ValueError("解析时钟设置错误")

        except Exception as e:
            raise

    # 串口设置页面
    @handle_exceptions
    def get_serial_test_info(self, url="/action/content?type=serial_test&func=get", command=None, configVariant={}):
        """
        获取串口设置的信息
        :param url:
        :return:
        串口名称
        if(parseInt(data.data.rsType[index-1], 10) == 0)
			{
				$("#a_serial_test" + index + "_t").text("COM" + index + "(232)");
			}
			else
			{
				$("#a_serial_test" + index + "_t").text("COM" + index + "(485)");
			}
		测试状态
		g_test_Serial 点击了开始就不为1
		rsRsult： 0：未测试 1:正常 2：异常，3：未知错误

		if(g_test_Serial == 0)
			{
				$("#a_serial_test" + index + "_v").text(getString("serial_test_unknown"));
			}
			else
			{
				if(parseInt(data.data.rsRsult[index-1], 10) == 0)
				{
					$("#a_serial_test" + index + "_v").text(getString("serial_test_unknown"));
				}
				else if(parseInt(data.data.rsRsult[index-1], 10) == 1)
				{
					$("#a_serial_test" + index + "_v").text(getString("serial_test_normal"));
				}
				else if(parseInt(data.data.rsRsult[index-1], 10) == 2)
				{
					$("#a_serial_test" + index + "_v").text(getString("serial_test_abnormal"));
				}
				else if(parseInt(data.data.rsRsult[index-1], 10) == 3)
				{
					$("#a_serial_test" + index + "_v").text(getString("serial_test_noinfo"));
				}
			}
            {
       "data" : {
          "canNum" : 0,
          "hidePreviewChannel" : 1,
          "rsExternValue" : null,
          "rsNum" : 1,
          "rsRsult" : [ 2 ],
          "rsType" : [ 0 ],
          "rscanRsult" : null,
          "serial_ExternCount" : 0
       },
       "result" : 0
    }
    """
        try:
            #先点击开始7/28
            # self.get_serial_test_set(configVariant={"startFlag": 1})
            # 获取串口设置
            serial_info = self.client.get(f"{self.base_url}{url}").json()
            self.logger.info(f"获取串口设置数据{json.dumps(serial_info)}")
            if serial_info.get("result") == 0 and serial_info.get("data", None) is not None and serial_info.get(
                    "data").get("rsRsult", None) is not None:
                # 成功
                rsRsult = serial_info.get("data").get("rsRsult")
                index = rsRsult[0]
                ret = Serial_Info[index]
                print(f"串口：{ret}")
                return Result.success(value={command: ret}, configVariant=configVariant)
            else:
                raise ValueError("串口数据解析有误")

        except Exception as e:
            raise

    # 串口测试 设置，要先开始不然切换了一直会正常，或者异常
    @handle_exceptions
    def get_serial_test_set(self, url="/action/content?type=serial_test&func=set", command=None, configVariant={}):
        """
        :param url:{
            "{\"startFlag\":0,\"startExrernFlag\":0}": ""
        }
        :param startFlag: 1：串口测试开 0停止
        :return:
        开始：{"startFlag":1}
        停止：{"startFlag":1,startExrernFlag:0}
        """
        try:
            # 获取串口设置
            startFlag = configVariant.get("startFlag", None)
            
            # 参数校验
            if startFlag is None:
                self.logger.error("缺少必要参数startFlag")
                return Result.error("缺少必要参数startFlag")
                
            # 类型校验 - 必须是字符串类型
            if not isinstance(startFlag, str):
                self.logger.error(f"参数类型错误，startFlag:{startFlag}必须是字符串类型")
                return Result.error("参数类型错误，startFlag必须是字符串类型")
                
            # 转换参数值
            try:
                startFlag_value = int(startFlag.strip())
                
                # 参数值校验
                if startFlag_value == 1:
                    params = {"startFlag": 1}
                elif startFlag_value == 0:
                    params = {"startFlag": 0, "startExrernFlag": 0}
                else:
                    self.logger.error(f"参数值错误，startFlag:{startFlag}只能为'0'或'1'")
                    return Result.error(f"参数值错误，startFlag只能为'0'或'1'")
            except ValueError:
                self.logger.error(f"参数格式错误，startFlag:{startFlag}必须是可转换为整数的字符串('0'或'1')")
                return Result.error("参数格式错误，startFlag必须是可转换为整数的字符串('0'或'1')")
                
            # 发送请求
            serial_test = self.client.post(f"{self.base_url}{url}", json=params).json()
            self.logger.info(f"设置串口测试返回数据{json.dumps(serial_test)}")
            
            if serial_test.get("result") == 0:
                # 成功
                data ="串口测试设置成功"
                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("设置串口数据错误")

        except Exception as e:
            raise

    # http://**************/action/content?type=Preview&func=ADAS_set
    # ADAS标定 /SPK（通过点击标定的方式，测试设备的语音包以及SPK功能），需要ai设置的通道设置adas为chn1的才成功
    # 界面 启动标定动作
    @handle_exceptions
    def get_adas_set(self, url="/action/content?type=Preview&func=ADAS_set", command=None, configVariant={}):
        """

        :return:
        {"BottomPoint_y_ADAS":929,"Pointx_ADAS":502,"Pointy_ADAS":509}
        """
        try:

            params = {"BottomPoint_y_ADAS": 1, "Pointx_ADAS": 1, "Pointy_ADAS": 1}
            adas_set = self.client.post(f"{self.base_url}{url}", json=params).json()
            self.logger.info(f"设置ADAS标定测试返回数据{json.dumps(adas_set)}")
            if adas_set.get("result") == 0:
                # 成功
                # data = adas_set.get("data")
                data = "标定成功"
                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("设置设置ADAS标定错误")

        except Exception as e:
            raise
    # http://**************/action/content?type=io&func=set
    #io报警设置
    @handle_exceptions
    def get_io_set(self, url="/action/content?type=io&func=set", command=None, configVariant={}):
        """
        把io设置为1 (联动输出)linkage，linkOutput为1
        	var outputArrObj = new Array();
	$("[name='linkage_output_channel']:checked").each(function(){
		outputArrObj.push($(this).val());
	});
	data.linkOutput = bitAndToInt(outputArrObj);
        data.linkage = data.linkOutput | (data.linkBuzzer << 2);
        :return:
        {"inNo":1,"enable":1,"limit":0,"delay":3,"holdtime":0,"linkLockChn":0,"linkUploadChn":0,"linkOutput":0,"linkSnapChn":0,"previewChn":0,"record":1,"linkBuzzer":0,"linkPreviewMode":0,"linkage":0}
        {
   "result" : 9
}

        """
        try:

            params = {"inNo":1,"enable":1,"limit":0,"delay":3,"holdtime":0,"linkLockChn":0,"linkUploadChn":0,"linkSnapChn":0,"previewChn":0,"record":1,"linkBuzzer":0,"linkPreviewMode":0}
            linkOutput = configVariant.get('linkOutput',"1")
            if not isinstance(linkOutput, str) or not linkOutput.isdigit():
                self.logger.error(f"传递的参数有误，linkOutput:{linkOutput}需要为字符串")
                return Result.error("传递的参数有误")
            else:
                linkOutput = int(linkOutput)
            params.update({"linkage": linkOutput, "linkOutput": linkOutput})

            io_set = self.client.post(f"{self.base_url}{url}", json=params).json()
            self.logger.info(f"设置IO报警返回数据{json.dumps(io_set)}")
            if io_set.get("result") == 9:
                # 成功
                # data = adas_set.get("data")
                data = "IO报警设置成功"
                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("设IO报警设置错误")

        except Exception as e:
            raise

    # http://**************/action/content?type=Software&func=set
    @handle_exceptions
    def get_built_software_set(self, url="/action/content?type=Software&func=set", command=None, configVariant={}):
        """
        AI报警页面 -->ai页面--》通道设置--》ADAS设置 http://**************/built_software.html
        {"aiSfOption":7,"aiSfOptionch1":0,"aiSfOptionch2":0,"aiSfOptionch3":0,"aiSfOptionch4":0,"aiVpcRes1":0,"aiVpcRes2":0,"aiVpcRes3":0,"aiVpcRes4":0,"aiSfKl":0,"aiDsm":0,"aidmsInstallationType":0,"aiDmsx":0,"aiAdas":1,"aiLeft":0,"aiRight":0,"aiBack":0,"aiHead":0,"aileftcamera":0,"airightcamera":0,"aiheadcamera":0,"aibackcamera":0,"set360":0,"show360_start_speed":null,"show360_end_speed":null}
       js 函数doSaveBuiltSoftware
       aiAdas：设置adas通道为关闭0或者是1,2,Spk需要chn1为1的才成功的
       """
        try:
            # 默认设置chn1
            aiAdas = 1
            params = {"aiSfOption": 7, "aiSfOptionch1": 0, "aiSfOptionch2": 0, "aiSfOptionch3": 0,
                      "aiSfOptionch4": 0,
                      "aiVpcRes1": 0, "aiVpcRes2": 0, "aiVpcRes3": 0, "aiVpcRes4": 0, "aiSfKl": 0, "aiDsm": 0,
                      "aidmsInstallationType": 0, "aiDmsx": 0, "aiLeft": 0, "aiRight": 0, "aiBack": 0,
                      "aiHead": 0, "aileftcamera": 0, "airightcamera": 0, "aiheadcamera": 0, "aibackcamera": 0,
                      "set360": 0, "show360_start_speed": None, "show360_end_speed": None}
            if configVariant:
                index = configVariant.get("aiAdas", "")
                # if isinstance(index, str) and index.isdigit():
                #     index = int(index)
                if not index or not isinstance(index, str) or not index.isdigit():
                    self.logger.error(f"传递的参数有误，index:{index}需要为字符串")
                    return Result.error(f"传递的参数有误")
                else:
                    aiAdas = int(index)
                    # 判断索引是否超过

            params.update({"aiAdas": aiAdas})
            adas_set = self.client.post(f"{self.base_url}{url}", json=params).json()
            self.logger.info(f"设置通道设置返回数据{json.dumps(adas_set)}")
            if adas_set.get("result") == 9:
                # 成功
                # data = adas_set.get("data")
                data = "通道设置成功"
                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("设置通道设置错误")

        except Exception as e:
            raise


    # http://**************/action/content?type=Preview_function&func=get_img获取照片,
    # 预览页面
    @handle_exceptions
    def get_img_preview(self, url="/action/content?type=Preview_function&func=get_img", command=None, configVariant={}):
        """
        获取通过的照片路径，其中根据设置的是全通道还是通道1，通道2返回多个还是一个
        :param url:
        :param command:
        :return:
                {
           "data" : {
              "AllPreviewChn" : 3,
              "ImgSrc_arr" : [
                 "/imagesm/snap1.jpg?time=1743500174",
                 "/imagesm/snap2.jpg?time=1743500174"
              ],
              "ImgSrc_mode" : 0,
              "get_chn_num" : 2
           },
           "result" : 0
        }{
           "data" : {
              "ImgSrc" : "/imagesm/snap1.jpg?time=1743500200",
              "ImgSrc_mode" : 1,
              "PreviewChn" : 1,
              "get_chn_num" : 2
           },
           "result" : 0
        }
        {
       "data" : {
          "ImgSrc" : "/imagesm/snap2.jpg?time=1743500441",
          "ImgSrc_mode" : 1,
          "PreviewChn" : 2,
          "get_chn_num" : 2
       },
       "result" : 0
    }
        """

        try:
            img_info = self.client.post(f"{self.base_url}{url}").json()
            self.logger.info(f"获取图片预览返回数据{json.dumps(img_info)}")
            if img_info.get("result") == 0:
                # 成功
                data = img_info.get("data")
                # 判断返回回来都是单通道还是全通道ImgSrc_arr
                # 单通道
                if "ImgSrc" in data and data.get("ImgSrc", False):
                    # 图片路径
                    imgSrc = data["ImgSrc"]
                    # 通道几
                    previewChn = data["PreviewChn"]

                    data = str(previewChn) + "," + imgSrc
                elif "ImgSrc_arr" in data and data.get('ImgSrc_arr', False):
                    ImgSrcs = data.get('ImgSrc_arr')
                    imgSrc = ",".join(ImgSrcs)
                    previewChn = data.get("AllPreviewChn")
                    data = str(previewChn) + "," + imgSrc
                else:
                    raise ValueError('图片预览解析识别')

                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("图片预览返回数据失败")

        except Exception as e:
            raise

    # / action / content?type = Preview_function & func = AllPreviewChn_set {"AllPreviewChn":0}	 ""{"AllPreviewChn":0}
    @handle_exceptions
    def all_preview_chn_set(self, url="/action/content?type=Preview_function&func=AllPreviewChn_set", command=None,
                            configVariant={}):
        """
        设置全通道预览
        :param url:
        :param command:
        :param configVariant:
        :return:
                {
           "result" : 0
        }
        """

        try:
            AllPreviewChn = 0
            params = {}
            if configVariant:
                index = configVariant.get("AllPreviewChn", "")
                # if isinstance(index, str) and index.isdigit():
                #     index = int(index)
                if not index or not isinstance(index, str) or not index.isdigit():
                    self.logger.error(f"传递的参数有误，AllPreviewChn:{index}需要为字符串")
                    return Result.error(f"传递的参数有误")
                else:
                    aiAdas = int(index)
                    # 判断索引是否超过

            params.update({"AllPreviewChn": AllPreviewChn})
            preview_all = self.client.post(f"{self.base_url}{url}", json=params).json()
            self.logger.info(f"全通道预览设置成功{json.dumps(preview_all)}")
            if preview_all.get("result") == 0:
                # 成功
                # data = adas_set.get("data")
                data = "全通道预览设置成功"
                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("全通道预览设置失败")

        except Exception as e:
            raise

    # http://**************/action/content?type=Preview_function&func=PreviewChn_set {"PreviewChn":1}	{"PreviewChn":2}	{
    @handle_exceptions
    def preview_chn_set(self, url='/action/content?type=Preview_function&func=PreviewChn_set', command="none",
                        configVariant={}):
        """
        设置预览图片通道
         {"PreviewChn":1}	{"PreviewChn":2}
        :param url:
        :param command:
        :param configVariant:
        :return:
        """
        try:
            PreviewChn = 1
            params = {}
            if configVariant:
                index = configVariant.get("PreviewChn", "")
                # if isinstance(index, str) and index.isdigit():
                #     index = int(index)
                if not index or not isinstance(index, str) or not index.isdigit():
                    self.logger.error(f"传递的参数有误，PreviewChn:{index}需要为字符串")
                    return Result.error(f"传递的参数有误")
                else:
                    aiAdas = int(index)
                    # 判断索引是否超过

            params.update({"PreviewChn": PreviewChn})
            preview_all = self.client.post(f"{self.base_url}{url}", json=params).json()
            self.logger.info(f"通道预览设置成功{json.dumps(preview_all)}")
            if preview_all.get("result") == 0:
                # 成功
                # data = adas_set.get("data")
                data = "通道预览设置成功"
                return Result.success(value={command: data}, configVariant=configVariant)
            else:
                # 失败
                raise ValueError("通道预览设置失败")

        except Exception as e:
            raise
# def download_preview_img(self,data):
#     if ImgSrc:
#         # 下载图片的地址
#         img_url = f"{self.base_url}/{ImgSrc}"
#         # 获取图片后缀
#         filenane, ext = os.path.splitext(ImgSrc)
#         ext = ext if ext else ".jpg"
#         # 获取路径
#         # ini获取图片路径
#         ImageDir = self.iniconfig.get("CrawlerConfing", "ImageDir", None)
#         ImageDir = ImageDir if ImageDir else "QrImages"
#         # 创建文件目录
#         if not os.path.exists(ImageDir):
#             os.makedirs(ImageDir)
#
#         save_path = f"{ImageDir}/{qr_name}{ext}"
#         self.logger.info(f"upload qr code path:{save_path}")
#         # 获取图片
#         try:
#             response = self.client.get(img_url)
#             if response.status_code == 200:
#                 with open(save_path, 'wb') as f:
#                     f.write(response.content)

    def refresh_session(self):
        """刷新会话，如果会话无效则重新登录"""
        try:
            if not self.client.session_valid:
                self.logger.info("会话无效，尝试重新登录...")
                self.client._login()
                return True
            return True
        except Exception as e:
            self.logger.error(f"刷新会话失败: {str(e)}")
            return False


if __name__ == '__main__':
    # CONFIG_FILE = "DvrSockerConfig.ini"
    # ini_config = INIConfigManager(CONFIG_FILE)
    # cralerfunc = CrawlerFunc(logger=None, iniconfig=ini_config)
    # cralerfunc.get_sys_info()
    # cralerfunc.get_sys_info(command="AiVersion")
    # cralerfunc.get_sys_info(command="Imei")

    try:
        parsed_value = int(str(1).strip())
        result = (parsed_value == 0)
        print(result)
    except ValueError:
        result = False
    # print(Disk_Info.values())
