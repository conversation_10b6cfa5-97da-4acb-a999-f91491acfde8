import configparser
import os
from pathlib import Path
from typing import Dict, Optional, Union, Any


class INIConfigManager:
    """区分大小写的INI配置文件操作类"""

    def __init__(self, file_path: Union[str, Path], default_config: Optional[Dict] = None):
        """
        初始化INI管理器
        :param file_path: INI文件路径
        :param default_config: 默认配置结构，示例:
            {
                "Section1": {
                    "Key1": "value1",
                    "Key2": 42
                },
                "Section2": {
                    "CaseSensitiveKey": True
                }
            }
        """
        self.file_path = Path(file_path)
        self.default_config = default_config or {}
        self.parser = configparser.ConfigParser()

        # 保持键名原始大小写
        self.parser.optionxform = lambda option: option  # type: ignore

        self._initialize_config()

    def _initialize_config(self) -> None:
        """初始化配置文件"""
        # 创建父目录
        self.file_path.parent.mkdir(parents=True, exist_ok=True)

        # 文件不存在时创建
        if not self.file_path.exists():
            self._create_config()
            print(f"Created new config file: {self.file_path}")
        else:
            self._load_and_update_config()

    def _create_config(self) -> None:
        """根据默认配置创建新文件"""
        for section, options in self.default_config.items():
            self.parser[section] = {k: str(v) for k, v in options.items()}
        self._save_config()

    def _load_and_update_config(self) -> None:
        """加载并更新现有配置"""
        self.parser.read(self.file_path)
        updated = False

        # 补充缺失的配置项
        for section, options in self.default_config.items():
            if not self.parser.has_section(section):
                self.parser.add_section(section)
                updated = True
                print(f"Added new section: [{section}]")

            for key, default_value in options.items():
                if not self.parser.has_option(section, key):
                    self.parser.set(section, key, str(default_value))
                    updated = True
                    print(f"Added new item: [{section}].{key} = {default_value}")

        if updated:
            self._save_config()

    def _save_config(self) -> None:
        """保存配置文件"""
        with open(self.file_path, 'w') as configfile:
            self.parser.write(configfile)

    def get(
            self,
            section: str,
            key: str,
            default: Optional[Any] = None,
            auto_type: bool = True
    ) -> Union[str, int, float, bool, None]:
        """
        获取配置值
        :param section: 节名称
        :param key: 键名称
        :param default: 默认值
        :param auto_type: 是否自动转换数据类型
        """
        try:
            value = self.parser.get(section, key)
            if not auto_type:
                return value

            # 自动类型转换
            try:
                return int(value)
            except ValueError:
                try:
                    return float(value)
                except ValueError:
                    lower_val = value.lower()
                    if lower_val in ('true', 'false'):
                        return lower_val == 'true'
                    return value
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default

    def set(
            self,
            section: str,
            key: str,
            value: Any,
            auto_create_section: bool = True
    ) -> None:
        """
        设置配置值
        :param section: 节名称
        :param key: 键名称
        :param value: 值
        :param auto_create_section: 是否自动创建不存在的节
        """
        if auto_create_section and not self.parser.has_section(section):
            self.parser.add_section(section)

        self.parser.set(section, key, str(value))
        self._save_config()

    def remove_key(self, section: str, key: str) -> bool:
        """删除指定键"""
        if self.parser.remove_option(section, key):
            self._save_config()
            return True
        return False

    def remove_section(self, section: str) -> bool:
        """删除整个节"""
        if self.parser.remove_section(section):
            self._save_config()
            return True
        return False

    def list_sections(self) -> list:
        """获取所有节名称"""
        return self.parser.sections()

    def list_keys(self, section: str) -> list:
        """获取指定节的所有键"""
        try:
            return self.parser.options(section)
        except configparser.NoSectionError:
            return []

    def show_all(self) -> None:
        """显示完整配置"""
        print(f"\n{' Config Content '.center(50, '=')}")
        for section in self.parser.sections():
            print(f"[{section}]")
            for key in self.parser[section]:
                value = self.get(section, key)
                print(f"  {key} = {value} ({type(value).__name__})")
            print()