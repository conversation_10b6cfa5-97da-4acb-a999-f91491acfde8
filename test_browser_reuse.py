# -*- coding: utf-8 -*-
"""
测试浏览器复用功能
"""

import json
import socket
import time


def send_command(command, config_variant=None):
    """发送命令到TCP服务器"""
    try:
        # 创建socket连接
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 8888))
        
        # 构造消息
        message = {
            "command": command
        }
        if config_variant:
            message["ConfigVariant"] = config_variant
        
        # 发送消息
        message_str = json.dumps(message)
        client_socket.send(message_str.encode('utf-8'))
        print(f"发送命令: {command}")
        
        # 接收响应
        response = client_socket.recv(1024).decode('utf-8')
        print(f"服务器响应: {response}")
        
        client_socket.close()
        return True
        
    except Exception as e:
        print(f"发送命令失败: {e}")
        return False


def test_browser_reuse():
    """测试浏览器复用功能"""
    print("=== 浏览器复用功能测试 ===")
    
    # 测试1: 打开wifi页面
    print("\n1. 打开wifi页面")
    send_command("wifi_page", {"keep_open": "5"})
    time.sleep(2)
    
    # 测试2: 打开system页面（应该复用浏览器）
    print("\n2. 打开system页面")
    send_command("system_page", {"keep_open": "5"})
    time.sleep(2)
    
    # 测试3: 打开preview页面
    print("\n3. 打开preview页面")
    send_command("preview_page", {"keep_open": "5"})
    time.sleep(2)
    
    # 测试4: 隐藏浏览器
    print("\n4. 隐藏浏览器")
    send_command("hide_browser")
    time.sleep(2)
    
    # 测试5: 显示浏览器
    print("\n5. 显示浏览器")
    send_command("show_browser")
    time.sleep(2)
    
    # 测试6: 关闭浏览器
    print("\n6. 关闭浏览器")
    send_command("close_browser")
    
    print("\n=== 测试完成 ===")


def test_performance():
    """测试性能对比"""
    print("\n=== 性能测试 ===")
    
    pages = ["wifi_page", "system_page", "preview_page"]
    
    # 测试复用模式的性能
    print("测试浏览器复用模式性能...")
    start_time = time.time()
    
    for page in pages:
        print(f"  打开 {page}")
        send_command(page, {"keep_open": "1"})
        time.sleep(1.5)  # 等待页面加载
    
    # 关闭浏览器
    send_command("close_browser")
    
    total_time = time.time() - start_time
    print(f"复用模式总耗时: {total_time:.2f} 秒")
    print(f"平均每页耗时: {total_time/len(pages):.2f} 秒")


def interactive_test():
    """交互式测试"""
    print("\n=== 交互式测试 ===")
    print("可用命令:")
    print("  wifi - 打开wifi页面")
    print("  system - 打开system页面")
    print("  preview - 打开preview页面")
    print("  hide - 隐藏浏览器")
    print("  show - 显示浏览器")
    print("  close - 关闭浏览器")
    print("  quit - 退出测试")
    
    command_map = {
        "wifi": "wifi_page",
        "system": "system_page", 
        "preview": "preview_page",
        "hide": "hide_browser",
        "show": "show_browser",
        "close": "close_browser"
    }
    
    while True:
        try:
            user_input = input("\n请输入命令: ").strip().lower()
            
            if user_input == "quit":
                print("退出测试")
                break
            
            if user_input in command_map:
                actual_command = command_map[user_input]
                if user_input in ["wifi", "system", "preview"]:
                    # 页面命令，询问保持时间
                    keep_open = input("保持显示时间(秒，默认5): ").strip()
                    if not keep_open:
                        keep_open = "5"
                    send_command(actual_command, {"keep_open": keep_open})
                else:
                    # 控制命令
                    send_command(actual_command)
            else:
                print("未知命令，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n测试被中断")
            break
        except Exception as e:
            print(f"测试出错: {e}")


if __name__ == "__main__":
    print("浏览器复用功能测试工具")
    print("请确保TCP服务器已启动 (端口8888)")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    try:
        # 基本功能测试
        test_browser_reuse()
        
        # 性能测试
        test_performance()
        
        # 交互式测试
        interactive_test()
        
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"测试失败: {e}")
    
    print("测试结束")
