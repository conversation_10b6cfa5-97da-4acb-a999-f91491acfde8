# 浏览器复用功能说明

## 概述

为了提高性能和减少资源消耗，现在支持浏览器复用功能。不再每次指令都启动新的浏览器，而是复用同一个浏览器实例。

## 主要特性

### 1. 浏览器复用
- 第一次调用时启动浏览器实例
- 后续调用复用同一个浏览器
- 大幅减少浏览器启动时间

### 2. 智能隐藏/显示
- `keep_open` 参数功能改变：
  - **复用模式**: 不再关闭浏览器，而是隐藏窗口
  - **传统模式**: 保持原有行为（关闭浏览器）

### 3. 向后兼容
- 现有代码无需修改即可工作
- 默认启用浏览器复用功能
- 可通过参数控制是否使用复用

## 使用方法

### 基本用法（推荐）

```python
# 启用浏览器复用（默认）
open_device_page('wifi', keep_open=5, reuse_browser=True)
open_device_page('system', keep_open=5, reuse_browser=True)
open_device_page('network', keep_open=5, reuse_browser=True)

# 手动关闭共享浏览器
close_shared_browser()
```

### 传统模式（如需要）

```python
# 禁用浏览器复用，每次创建新浏览器
open_device_page('wifi', keep_open=5, reuse_browser=False)
```

### 异步函数

```python
# 异步函数也支持浏览器复用
open_device_page_async('wifi', reuse_browser=True)
open_device_page_async('system', reuse_browser=True)
```

## 新增函数

### 浏览器管理函数

```python
# 完全关闭共享浏览器
close_shared_browser()

# 显示隐藏的浏览器窗口
show_shared_browser()

# 隐藏浏览器窗口
hide_shared_browser()
```

## 参数说明

### open_device_page() 新增参数

- `reuse_browser=True`: 是否使用浏览器复用
  - `True`: 复用模式（推荐）
  - `False`: 传统模式

### open_device_page_async() 新增参数

- `reuse_browser=True`: 是否使用浏览器复用

### keep_open 参数行为变化

| 模式 | keep_open > 0 | 行为 |
|------|---------------|------|
| 复用模式 | 是 | 显示指定时间后隐藏浏览器 |
| 复用模式 | 否 | 立即隐藏浏览器 |
| 传统模式 | 是 | 显示指定时间后关闭浏览器 |
| 传统模式 | 否 | 立即关闭浏览器 |

## 性能优势

### 启动时间对比

- **传统模式**: 每次 3-5 秒启动时间
- **复用模式**: 首次 3-5 秒，后续 < 1 秒

### 资源消耗

- **传统模式**: 每个浏览器实例约 100-200MB 内存
- **复用模式**: 只有一个浏览器实例

## 使用建议

### 推荐场景

1. **批量操作**: 需要连续访问多个页面
2. **频繁调用**: 短时间内多次调用
3. **资源受限**: 内存或CPU资源有限的环境

### 不推荐场景

1. **长时间间隔**: 两次调用间隔很长（如几小时）
2. **不同设备**: 需要同时访问多个不同IP的设备
3. **并发需求**: 需要同时打开多个页面

### 最佳实践

```python
# 1. 批量操作时使用复用模式
pages = ['wifi', 'system', 'network', 'disk']
for page in pages:
    open_device_page(page, keep_open=2, reuse_browser=True)
close_shared_browser()  # 完成后关闭

# 2. 单次操作可使用传统模式
open_device_page('wifi', keep_open=10, reuse_browser=False)

# 3. 手动控制显示状态
open_device_page('wifi', reuse_browser=True)
hide_shared_browser()  # 隐藏到后台
# ... 其他操作 ...
show_shared_browser()  # 需要时显示
```

## 注意事项

1. **浏览器状态**: 复用的浏览器会保持登录状态
2. **内存管理**: 长时间运行建议定期调用 `close_shared_browser()`
3. **错误处理**: 如果浏览器崩溃，下次调用会自动重新启动
4. **并发限制**: 同一时间只能有一个共享浏览器实例

## 故障排除

### 浏览器无响应

```python
# 强制关闭并重新启动
close_shared_browser()
open_device_page('wifi', reuse_browser=True)
```

### 内存占用过高

```python
# 定期清理
close_shared_browser()
```

### 页面加载异常

```python
# 临时使用传统模式
open_device_page('wifi', reuse_browser=False)
```
